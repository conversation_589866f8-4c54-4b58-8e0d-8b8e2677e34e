# FTP处理逻辑调试打印说明

## 添加的调试打印位置和内容

### 1. 控制流初始化调试打印

**位置**: `dissect_ftp_control` 函数中的session初始化
**打印内容**:
- `FTP: Initialized as client->server, control_port_dst=21, control_port_src=xxxx`
- `FTP: Initialized as server->client, control_port_dst=21, control_port_src=xxxx`

### 2. 方向判断调试打印

**位置**: `dissect_ftp_control` 函数中的方向判断逻辑
**打印内容**:
- `FTP: Direction SRC2DST (client->server), port_dst=21, port_src=xxxx`
- `FTP: Direction DST2SRC (server->client), port_dst=xxxx, port_src=21`
- `FTP: Direction SRC2DST (fallback), port_dst=xxxx, control_port_dst=xxxx`

### 3. PORT/PASV命令处理调试打印

**位置**: PORT/PASV命令处理后的conversation创建
**打印内容**:
- `FTP: Created conversation for PORT/PASV - IP x.x.x.x:port -> x.x.x.x:port, data_port=xxxx`
- `FTP: data_conv_tuple - proto=6, port_src=xxxx, port_dst=xxxx`
- `FTP: Single packet processing - PORT/PASV parsed, file_num=x, dataPort=xxxx`
- `FTP: Found pending filename 'xxx', associating with port xxxx` (如果有待关联文件名)

### 4. EPRT命令处理调试打印

**位置**: EPRT命令处理
**打印内容**:
- `FTP: Created conversation for EPRT - data_port=xxxx`
- `FTP: EPRT data_conv_tuple - proto=6, port_src=xxxx, port_dst=xxxx`
- `FTP: Single packet processing - EPRT parsed, file_num=x, dataPort=xxxx`

### 5. EPASV命令处理调试打印

**位置**: EPASV命令处理
**打印内容**:
- `FTP: Created conversation for EPASV - data_port=xxxx`
- `FTP: EPASV data_conv_tuple - proto=6, port_src=xxxx, port_dst=xxxx`
- `FTP: Single packet processing - EPASV parsed, file_num=x, dataPort=xxxx`

### 6. 150响应文件名提取调试打印

**位置**: 150响应处理
**打印内容**:
- `FTP: Single packet - extracted filename from 150 response: 'xxx'`
- `FTP: No empty port entry found, storing filename 'xxx' in pending storage`
- `FTP: Updated empty storpath at index x (port x) with 'xxx'`

### 7. 数据流处理调试打印

**位置**: `dissect_ftp_data_rsm` 函数
**打印内容**:
- `FTP: Data flow processing - port_src=xxxx, port_dst=xxxx, direction=x`
- `FTP: Using existing data_conv_tuple - proto=6, port_src=xxxx, port_dst=xxxx`
- `FTP: Constructing tuple for control conv lookup`
- `FTP: DST2SRC - constructed tuple port_src=xxxx` 或 `FTP: SRC2DST - constructed tuple port_src=xxxx`
- `FTP: Looking for control conv with tuple port_src=xxxx`

### 8. Control Conversation查找调试打印

**打印内容**:
- `FTP: Found control conv, updating data session`
- `FTP: No control conv found for data flow`
- `FTP: Data flow - updated session from control, filename_list count=x`
- `FTP: Control session filename_list count=x`
- `FTP: Control filename_list[x]: port=xxxx, storpath='xxx'`

### 9. 文件名查找调试打印

**位置**: 数据流处理中的文件名查找
**打印内容**:
- `FTP: Data flow processing, looking for filename with port xxxx`
- `FTP: Current session filename_list count=x`
- `FTP: Current filename_list[x]: port=xxxx, storpath='xxx'`
- `FTP: Found entry in current session for port xxxx: storpath='xxx'`
- `FTP: No entry found in current session for port xxxx`
- `FTP: Searching in control session filename list, count=x`
- `FTP: Found entry in control session for port xxxx: storpath='xxx'`
- `FTP: No entry found in control session for port xxxx`

### 10. 文件名列表查找函数调试打印

**位置**: `ftp_filename_list_find_by_port` 函数
**打印内容**:
- `FTP: Searching for port xxxx in filename list with x entries`
- `FTP: Checking entry x: port=xxxx, storpath='xxx'`
- `FTP: Found matching port xxxx at index x`
- `FTP: No matching port xxxx found in filename list`

## 关键调试流程

1. **控制流建立**: 查看初始化和方向判断是否正确
2. **PORT命令处理**: 查看端口是否正确添加到文件名列表
3. **150响应处理**: 查看文件名是否正确提取和关联
4. **数据流处理**: 查看是否能找到对应的control conversation
5. **文件名查找**: 查看是否能在文件名列表中找到对应端口的文件名

## 预期的正常流程日志

```
FTP: Initialized as client->server, control_port_dst=21, control_port_src=xxxx
FTP: Direction SRC2DST (client->server), port_dst=21, port_src=xxxx
FTP: Created conversation for PORT/PASV - IP x.x.x.x:xxxx -> x.x.x.x:xxxx, data_port=4170
FTP: Single packet processing - PORT/PASV parsed, file_num=1, dataPort=4170
FTP: Single packet - extracted filename from 150 response: '100-gdb-tips.pdf'
FTP: Updated empty storpath at index 0 (port 4170) with '100-gdb-tips.pdf'
FTP: Data flow processing - port_src=4170, port_dst=xxxx, direction=x
FTP: Found control conv, updating data session
FTP: Found filename '100-gdb-tips.pdf' for port 4170
```
