/****************************************************************************************
 * 文 件 名 : dpi_ftp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include "dpi_flow.h"
#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "dpi_detect.h"
#include "dpi_proto_ids.h"
#include "dpi_pschema.h"
#include "dpi_utils.h"

#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>

#include "pch.h"
#include "utils/dpi_utils.h"
#include <netinet/in.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <iconv.h>
#define EPRT_AF_IPv4 1
#define EPRT_AF_IPv6 2

#define FTP_CONTROL_PORT 21
#define FTP_DATA_PORT 20
#define FTP_MAX_SEARCH  128
#define FTP_FILENAME_INITIAL_CAPACITY 20
#define FTP_FILENAME_GROW_FACTOR 2

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
int isUTF8(const char *pData, int len);

// 动态文件名管理函数声明
static int ftp_filename_list_init(struct ftp_filename_list *list);
static void ftp_filename_list_free(struct ftp_filename_list *list);
static int ftp_filename_list_add(struct ftp_filename_list *list, uint16_t dataPort, const char *storpath);
static struct ftp_port_filename* ftp_filename_list_find_by_port(struct ftp_filename_list *list, uint16_t dataPort);
static struct ftp_port_filename* ftp_filename_list_find_empty_port(struct ftp_filename_list *list);
static struct ftp_port_filename* ftp_filename_list_find_empty_storpath(struct ftp_filename_list *list, uint16_t dataPort);
static void ftp_update_all_data_convs(struct ftp_session *control_session);
static int ftp_filename_list_update_latest_empty_storpath(struct ftp_filename_list *list, const char *storpath, struct flow_info *flow, struct ftp_session *session);
static int ftp_filename_list_update_latest_storpath_force(struct ftp_filename_list *list, const char *storpath, struct flow_info *flow, struct ftp_session *session);

/* add by liugh */
extern int session_protocol_st_size[PROTOCOL_MAX];

// 动态文件名管理函数实现
static int ftp_filename_list_init(struct ftp_filename_list *list) {
    list->filenames = dpi_malloc(FTP_FILENAME_INITIAL_CAPACITY * sizeof(struct ftp_port_filename));
    if (!list->filenames) {
        return -1;
    }
    memset(list->filenames, 0, FTP_FILENAME_INITIAL_CAPACITY * sizeof(struct ftp_port_filename));
    list->capacity = FTP_FILENAME_INITIAL_CAPACITY;
    list->count = 0;
    return 0;
}

static void ftp_filename_list_free(struct ftp_filename_list *list) {
    if (list->filenames) {
        dpi_free(list->filenames);
        list->filenames = NULL;
    }
    list->capacity = 0;
    list->count = 0;
}

static int ftp_filename_list_expand(struct ftp_filename_list *list) {
    if (!list) {
        printf("FTP: Cannot expand NULL filename list\n");
        return -1;
    }

    uint16_t new_capacity = list->capacity * FTP_FILENAME_GROW_FACTOR;
    if (new_capacity <= list->capacity) {
        printf("FTP: Invalid new capacity %d (current: %d)\n", new_capacity, list->capacity);
        return -1;
    }

    struct ftp_port_filename *new_filenames = dpi_malloc(new_capacity * sizeof(struct ftp_port_filename));
    if (!new_filenames) {
        printf("FTP: Failed to allocate memory for expanded filename list\n");
        return -1;
    }

    // 安全复制现有数据
    if (list->filenames && list->capacity > 0) {
        memcpy(new_filenames, list->filenames, list->capacity * sizeof(struct ftp_port_filename));
    }

    // 清零新分配的部分
    memset(new_filenames + list->capacity, 0, (new_capacity - list->capacity) * sizeof(struct ftp_port_filename));

    dpi_free(list->filenames);
    list->filenames = new_filenames;
    list->capacity = new_capacity;
    printf("FTP: Expanded filename list capacity from %d to %d\n", list->capacity / FTP_FILENAME_GROW_FACTOR, new_capacity);
    return 0;
}

static int ftp_filename_list_add(struct ftp_filename_list *list, uint16_t dataPort, const char *storpath) {
    printf("FTP: Adding port %d to filename list, storpath=%s, current count=%d\n",
            dataPort, storpath ? storpath : "NULL", list->count);

    // 查找空闲位置
    for (int i = 0; i < list->capacity; i++) {
        if (list->filenames[i].dataPort == 0) {
            list->filenames[i].dataPort = dataPort;
            if (storpath) {
                strncpy(list->filenames[i].storpath, storpath, sizeof(list->filenames[i].storpath) - 1);
                printf("FTP: Set storpath at index %d: %s\n", i, storpath);
            }
            if (i >= list->count) {
                list->count = i + 1;
            }
            printf("FTP: Added port %d at index %d, new count=%d\n", dataPort, i, list->count);
            return 0;
        }
    }

    // 需要扩展
    printf("FTP: Expanding filename list from capacity %d\n", list->capacity);
    if (ftp_filename_list_expand(list) < 0) {
        printf( "FTP: Failed to expand filename list\n");
        return -1;
    }

    list->filenames[list->count].dataPort = dataPort;
    if (storpath) {
        strncpy(list->filenames[list->count].storpath, storpath, sizeof(list->filenames[list->count].storpath) - 1);
        printf("FTP: Set storpath at new index %d: %s\n", list->count, storpath);
    }
    list->count++;
    printf("FTP: Added port %d at new index %d, new count=%d\n", dataPort, list->count - 1, list->count);
    return 0;
}

static struct ftp_port_filename* ftp_filename_list_find_by_port(struct ftp_filename_list *list, uint16_t dataPort) {
    printf("FTP: Searching for port %d in filename list with %d entries\n", dataPort, list->count);
    for (int i = 0; i < list->count; i++) {
        printf("FTP: Checking entry %d: port=%d, storpath='%s'\n",
               i, list->filenames[i].dataPort, list->filenames[i].storpath);
        if (list->filenames[i].dataPort == dataPort) {
            printf("FTP: Found matching port %d at index %d\n", dataPort, i);
            return &list->filenames[i];
        }
    }
    printf("FTP: No matching port %d found in filename list\n", dataPort);
    return NULL;
}

static struct ftp_port_filename* ftp_filename_list_find_empty_port(struct ftp_filename_list *list) {
    for (int i = 0; i < list->capacity; i++) {
        if (list->filenames[i].dataPort == 0) {
            return &list->filenames[i];
        }
    }
    return NULL;
}

static struct ftp_port_filename* ftp_filename_list_find_empty_storpath(struct ftp_filename_list *list, uint16_t dataPort) {
    for (int i = list->count - 1; i >= 0; i--) {
        if (list->filenames[i].dataPort == dataPort && list->filenames[i].storpath[0] == '\0') {
            return &list->filenames[i];
        }
    }
    return NULL;
}



int decode_IOS8859_to_uft8(char *inbuf, int inlen, char *outbuf, int outlen) {
  iconv_t cd;
  char *ptr_in = inbuf;
  char *ptr_out = outbuf;

  size_t local_inlen = inlen;
  size_t local_outlen = outlen;
  if (isUTF8(inbuf, inlen)) {
    return -1;
  }
  cd = iconv_open("utf-8", "GBK");  //gb2312
  if (0 == cd) {
    // printf("iconv open failed!\n");
    return -1;
  }

  if (-1 == (int)iconv(cd, (char **)&ptr_in, (size_t *)&local_inlen, (char **)&ptr_out, (size_t *)&local_outlen)) {
    // printf("iconv failed !, errno %d\n", errno);
    snprintf((char *)outbuf, (unsigned int)outlen, "%s", inbuf);
    iconv_close(cd);
    return -1;
  }

  iconv_close(cd);
  return 0;
}
struct ftp_info
{
    char      software[64];
    char      hostName[64];
    char      username[COMMON_NAME_PASSWD_LEN];
    char      password[COMMON_NAME_PASSWD_LEN];
    char      filename[COMMON_FILE_PATH];        //文件名
    uint64_t  filesize;

    uint16_t  ctl_srcport;        //源端口
    uint16_t  ctl_dstport;        //目的端口
    uint8_t   is_request;
    char      cmd_or_code[COMMON_FILE_PATH];
    char      args[COMMON_FILE_PATH];
    char      met[COMMON_FILE_PATH];                //发送的ftp命令
    char      met_contend[COMMON_FILE_PATH];        //ftp命令参数
    char      ret_code[8];            //服务器响应码
    char      ret_con[COMMON_FILE_PATH];            //返回消息内容
    int       offset;                    //文件传输起始偏移位置
    char      con_type[8];            //传输文件类型
    char     login_status[COMMON_STATUS_LEN];
    char      server_ip[64];
};

enum ftp_index_em{
    EM_FTP_TYPE,
    EM_FTP_USERNAME,
    EM_FTP_PASSWORD,
    EM_FTP_FILENAME,
    EM_FTP_CONTROL_SRCPORT,
    EM_FTP_CONTROL_DSTPORT,
    EM_FTP_USER_FILE_PATH,
    EM_FTP_CONTROL_CMD,
    EM_FTP_CONTROL_ARGS,
    EM_FTP_SERVER_NAME,
    EM_FTP_SERVER_IP,
    EM_FTP_DATA_FILETYPE,
    EM_FTP_DATA_FILEPATH,
    EM_FTP_MET,
    EM_FTP_MET_CONTEND,
    EM_FTP_RET_CODE,
    EM_FTP_RET_CON,
    EM_FTP_OFFSET,
    EM_FTP_CON_TYPE,
    EM_FTP_DATA_PORT,
    EM_FTP_FILE_SIZE,
    EM_FTP_DATA_IP,
    EM_FTP_MODE,
    EM_FTP_DATA_TOTAL_LEN,  // for test
    EM_FTP_DATA_REAL_LEN,   // for test
    EM_FTP_LOGIN_STATUS,
    EM_FTP_DATA_HOSTNAME,
    EM_FTP_DATA_SOFTWARE,
#ifdef DPI_SDT_YNAO
    EM_FTP_DATA_LOCALFILENAME,
    EM_FTP_DATA_LOCALFILEPATH,
    EM_FTP_DATA_FILE_CONTENT,
    EM_FTP_DATA_OPERATIONS,
#endif
    EM_FTP_MAX
};

static dpi_field_table  ftp_field_array[] = {
    DPI_FIELD_D(EM_FTP_TYPE,                        YA_FT_UINT16,       "Ftp_type"),
    DPI_FIELD_D(EM_FTP_USERNAME,                    YA_FT_STRING,       "Username"),
    DPI_FIELD_D(EM_FTP_PASSWORD,                    YA_FT_STRING,       "Password"),
    DPI_FIELD_D(EM_FTP_FILENAME,                    YA_FT_STRING,       "Filename"),
    DPI_FIELD_D(EM_FTP_CONTROL_SRCPORT,             YA_FT_UINT16,       "Control_srcport"),
    DPI_FIELD_D(EM_FTP_CONTROL_DSTPORT,             YA_FT_UINT16,       "Control_dstport"),
    DPI_FIELD_D(EM_FTP_USER_FILE_PATH,              YA_FT_STRING,       "User_filepath"),
    DPI_FIELD_D(EM_FTP_CONTROL_CMD,                 YA_FT_STRING,       "Control_cmd"),
    DPI_FIELD_D(EM_FTP_CONTROL_ARGS,                YA_FT_STRING,       "Control_args"),
    DPI_FIELD_D(EM_FTP_SERVER_NAME,                 YA_FT_STRING,       "Server_name"),
    DPI_FIELD_D(EM_FTP_SERVER_IP,                   YA_FT_STRING,       "Server_IP"),
    DPI_FIELD_D(EM_FTP_DATA_FILETYPE,               YA_FT_STRING,       "Data_filetype"),
    DPI_FIELD_D(EM_FTP_DATA_FILEPATH,               YA_FT_STRING,       "Data_filepath"),
    DPI_FIELD_D(EM_FTP_MET,                         YA_FT_STRING,       "Method"),
    DPI_FIELD_D(EM_FTP_MET_CONTEND,                 YA_FT_STRING,       "Method_content"),
    DPI_FIELD_D(EM_FTP_RET_CODE,                    YA_FT_INT32,        "Return_code"),
    DPI_FIELD_D(EM_FTP_RET_CON,                     YA_FT_STRING,       "Return_content"),
    DPI_FIELD_D(EM_FTP_OFFSET,                      YA_FT_STRING,       "Offset"),
    DPI_FIELD_D(EM_FTP_CON_TYPE,                    YA_FT_STRING,       "Content_type"),
    DPI_FIELD_D(EM_FTP_DATA_PORT,                   YA_FT_UINT32,       "DataPort"),
    DPI_FIELD_D(EM_FTP_FILE_SIZE,                   YA_FT_UINT64,       "FileSize"),
    DPI_FIELD_D(EM_FTP_DATA_IP,                     YA_FT_UINT32,       "DataIp"),
    DPI_FIELD_D(EM_FTP_MODE,                        YA_FT_UINT8,        "Mode"),
    DPI_FIELD_D(EM_FTP_DATA_TOTAL_LEN,              YA_FT_STRING,       "Data_totallen"),
    DPI_FIELD_D(EM_FTP_DATA_REAL_LEN,               YA_FT_STRING,       "Data_reallen"),
    DPI_FIELD_D(EM_FTP_LOGIN_STATUS,                YA_FT_UINT8,        "LoginStatus"),
    DPI_FIELD_D(EM_FTP_DATA_HOSTNAME,               YA_FT_STRING,       "hostName"),
    DPI_FIELD_D(EM_FTP_DATA_SOFTWARE,               YA_FT_STRING,       "software"),
#ifdef DPI_SDT_YNAO
    DPI_FIELD_D(EM_FTP_DATA_LOCALFILENAME,               YA_FT_STRING,       "localFilename"),
    DPI_FIELD_D(EM_FTP_DATA_LOCALFILEPATH,               YA_FT_STRING,       "localFilepath"),
    DPI_FIELD_D(EM_FTP_DATA_FILE_CONTENT,               YA_FT_STRING,       "ftp_file_content"),
    DPI_FIELD_D(EM_FTP_DATA_OPERATIONS,               YA_FT_STRING,       "ftp_operations"),
#endif
};


/*
*ftp的tbl日志函数
*/
static
void ftp_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct ftp_info *info, int *idx, int i)
{
  struct ftp_session *session = (struct ftp_session *)flow->app_session;
    switch(i){
    case EM_FTP_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)"ftp-control", strlen("ftp-control"));
        break;
    case EM_FTP_USERNAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->username, strlen(info->username));
        break;
    case EM_FTP_PASSWORD:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->password,strlen(info->password));
        break;
    case EM_FTP_FILENAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->filename,strlen(info->filename));
        break;
    case EM_FTP_CONTROL_CMD:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->cmd_or_code, strlen(info->cmd_or_code));
        break;
    case EM_FTP_CONTROL_ARGS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->args, strlen(info->args));
        break;
    case EM_FTP_CONTROL_DSTPORT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ntohs(info->ctl_dstport));
        break;
    case EM_FTP_CONTROL_SRCPORT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ntohs(info->ctl_srcport));
        break;
    case EM_FTP_MET:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->met, strlen(info->met));
        break;
    case EM_FTP_MET_CONTEND:
        // write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->met, strlen(info->met));
        // log_ptr->record[*idx - 1] = ' ';
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->met_contend, strlen(info->met_contend));
        break;
    case EM_FTP_RET_CODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, atoi(info->ret_code));
        // write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->ret_code, strlen(info->ret_code));
        break;
    case EM_FTP_RET_CON:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->ret_con, strlen(info->ret_con));
        break;
    case EM_FTP_OFFSET:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->offset);
        break;
    case EM_FTP_CON_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->con_type, strlen(info->con_type));
        break;
    case EM_FTP_LOGIN_STATUS:
        if (strncmp(info->login_status, "YES", strlen("YES")) == 0) {
          write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        } else {
          write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 0);
        }
        // write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->login_status, strlen((const char *)info->login_status));
        break;
    case EM_FTP_FILE_SIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->filesize);
        break;
    case EM_FTP_DATA_PORT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, htons(session->ftp_port));
        break;
    case EM_FTP_SERVER_IP:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->server_ip, strlen((const char *)info->server_ip));
        break;
    case EM_FTP_DATA_HOSTNAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->hostName,
            strlen((const char *)info->hostName));
        break;
    case EM_FTP_DATA_SOFTWARE:
        {
          char str_[2048] = {0};
          if (strlen(info->software)!= 0) {
            snprintf(str_, sizeof(str_), "%s %s ", info->hostName, info->software);
          }
          write_coupler_log(
              log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)str_, strlen((const char *)str_));
        }
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    return;
}

/*ftp control的识别函数，主要基于端口识别，增加一些过滤判断*/
static int identify_ftp_control(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    int line_len;
    int search_len = 0;

    if (g_config.protocol_switch[PROTOCOL_FTP_CONTROL] == 0)
        return 0;

    if (payload_len > 1460) {
        return 0;
    }

    search_len = FTP_MAX_SEARCH > payload_len ? payload_len : FTP_MAX_SEARCH;

    /* Exclude SMTP, which uses similar commands. */
    if (flow->tuple.inner.port_src == htons(25) || flow->tuple.inner.port_dst == htons(25)) {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_FTP_CONTROL);
        return 0;
    }

    if (flow->pkt_first_line.has_search == 0) {
        line_len = find_packet_line_end(payload, search_len);
        flow->pkt_first_line.has_search = 1;
        flow->pkt_first_line.linelen = line_len;
    } else {
        line_len = flow->pkt_first_line.linelen;
    }

    if (line_len <= 0)
        return 0;

    if(likely(0==g_config.ftp_identify_flag)){
        if (flow->tuple.inner.port_src == htons(g_config.ftp_port) || flow->tuple.inner.port_dst == htons(g_config.ftp_port)) {
            flow->real_protocol_id = PROTOCOL_FTP_CONTROL;
        }
    }

    return PROTOCOL_FTP_CONTROL;
}

static uint8_t isvalid_rfc2428_delimiter(const char c)
{
    /* RFC2428 sect. 2 states rules for a valid delimiter */
    const char *forbidden = "0123456789abcdef.:";
    if (!isgraph(c))
        return 0;
    if (strchr(forbidden, tolower(c)))
        return 0;
    return 1;
}

/*
*此函数用于ftp-data会话的识别，port请求和pasv响应中会有ip和端口号，目前ftp-data的识别可能不需要
*/
static int parse_port_pasv(const uint8_t *line, int linelen, uint32_t *ftp_ip, uint16_t *ftp_port)
{
    char     args[1024];
    char     *p;
    char      c;
    int       i;
    int       ip_address[4], port[2];
    uint8_t   ret = 0;

    size_t copy_len =  (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
    strncpy(args, (const char *)line, copy_len);
    args[copy_len] = 0;

    p = args;

    for (;;) {
        /*
        * Look for a digit.
        */
        while ((c = *p) != '\0' && !isdigit(c))
            p++;

        if (*p == '\0') {
            /*
            * We ran out of text without finding anything.
            */
            break;
        }

        /*
        * See if we have six numbers.
        */
        i = sscanf(p, "%d,%d,%d,%d,%d,%d",
                &ip_address[0], &ip_address[1], &ip_address[2], &ip_address[3],
                &port[0], &port[1]);
        if (i == 6) {
            /*
            * We have a winner!
            */
            *ftp_port = ((port[0] & 0xFF) << 8) | (port[1] & 0xFF);
            *ftp_ip = htonl((ip_address[0] << 24) | (ip_address[1] << 16) | (ip_address[2] << 8) | ip_address[3]);
            ret = 1;
            break;
        }

        /*
        * Well, that didn't work.  Skip the first number we found,
        * and keep trying.
        */
        while ((c = *p) != '\0' && isdigit(c))
            p++;
    }

    return ret;
}

/*eprt命令的解析，和port，pasv类似*/
static uint8_t parse_eprt_request(const uint8_t *line, int linelen, uint32_t *eprt_af,
        uint32_t *eprt_ip, uint16_t *eprt_ipv6, uint16_t *ftp_port)
{
    int      delimiters_seen = 0;
    char     delimiter;
    int      fieldlen;
    char    *field;
    int      n;
    int      lastn;
    char     *p;
    char      args[1024];
    uint8_t   ret = 0;
    size_t copy_len;


    /* line contains the EPRT parameters, we need at least the 4 delimiters */
    if (!line || linelen < 4)
        return 0;

    copy_len = (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
    strncpy(args, (const char *)line, copy_len);
    args[copy_len] = 0;

    p = args;
    /*
    * Handle a NUL being in the line; if there's a NUL in the line,
    * strlen(args) will terminate at the NUL and will thus return
    * a value less than linelen.
    */
    if (strlen(args) < (unsigned)linelen)
        linelen = strlen(args);

    /*
    * RFC2428 sect. 2 states ...
    *
    *     The EPRT command keyword MUST be followed by a single space (ASCII
    *     32). Following the space, a delimiter character (<d>) MUST be
    *     specified.
    *
    * ... the preceding <space> is already stripped so we know that the first
    * character must be the delimiter and has just to be checked to be valid.
    */
    if (!isvalid_rfc2428_delimiter(*p))
        return 0;  /* EPRT command does not follow a vaild delimiter;
                    * malformed EPRT command - immediate escape */

    delimiter = *p;
    /* Validate that the delimiter occurs 4 times in the string */
    for (n = 0; n < linelen; n++) {
        if (*(p + n) == delimiter)
        delimiters_seen++;
    }

    if (delimiters_seen != 4)
        return 0; /* delimiter doesn't occur 4 times
                    * probably no EPRT request - immediate escape */

    /* we know that the first character is a delimiter... */
    delimiters_seen = 1;
    lastn = 0;
    /* ... so we can start searching from the 2nd onwards */
    for (n = 1; n < linelen; n++) {

        if (*(p + n) != delimiter)
            continue;

        /* we found a delimiter */
        delimiters_seen++;

        fieldlen = n - lastn - 1;
        if (fieldlen <= 0)
            return 0; /* all fields must have data in them */
        field = p + lastn + 1;

        if (delimiters_seen == 2) {     /* end of address family field */
            char af_str[64] = {0};
            strncpy(af_str, field, (uint32_t)fieldlen >= sizeof(af_str) ? (sizeof(af_str) - 1) : (uint32_t)fieldlen);
            *eprt_af = atoi(af_str);
        }
        else if (delimiters_seen == 3) {/* end of IP address field */
            char ip_str[64] = {0};
            strncpy(ip_str, field, (uint32_t)fieldlen >= sizeof(ip_str) ? (sizeof(ip_str) - 1) : (uint32_t)fieldlen);

            if (*eprt_af == EPRT_AF_IPv4) {
                if (inet_pton(AF_INET, ip_str, eprt_ip))
                    ret = 1;
                else
                    ret = 0;
            }
            else if (*eprt_af == EPRT_AF_IPv6) {
                if (inet_pton(AF_INET6, ip_str, eprt_ipv6))
                    ret = 1;
                else
                    ret = 0;
            }
            else
                return 0; /* invalid/unknown address family */

        }
        else if (delimiters_seen == 4) {/* end of port field */
            char pt_str[64] = {0};
            strncpy(pt_str, field, (uint32_t)fieldlen >= sizeof(pt_str) ? (sizeof(pt_str) - 1) : (uint32_t)fieldlen);
            *ftp_port = (uint16_t)atoi(pt_str);
        }
        lastn = n;
    }

    return ret;
}

/*epasv命令的解析，和port，pasv类似*/
static uint8_t parse_extended_pasv_response(const uint8_t *line, int linelen, uint16_t *ftp_port)
{
    int          n;
    char      args[1024];
    char      *p;
    char       c;
    uint8_t   ret = 0;
    uint8_t   delimiters_seen = 0;
    size_t    copy_len;
    /*
     * Copy the rest of the line into a null-terminated buffer.
     */
    copy_len = (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
    strncpy(args, (const char *)line, copy_len);
    args[copy_len] = 0;

    p = args;

    /*
     * Look for ( <d> <d> <d>
       (Try to cope with '(' in description)
     */
    for ( ; !delimiters_seen; ) {
        char delimiter = '\0';
        while ((c = *p) != '\0' && (c != '('))
            p++;

        if (*p == '\0') {
            return 0;
        }

        /* Skip '(' */
        p++;

        /* Make sure same delimiter is used 3 times */
        for (n = 0; n < 3; n++) {
            if ((c = *p) != '\0') {
                if (delimiter == '\0' && isvalid_rfc2428_delimiter(c)) {
                    delimiter = c;
                }
                if (c != delimiter) {
                    break;
                }
                p++;
            }
            else {
                break;
            }
        }
        delimiters_seen = 1;
    }

    /*
     * Should now be at digits.
     */
    if (*p != '\0') {
        int port_valid = atoi(p);

        if (port_valid > 0)
            *ftp_port = (uint16_t)port_valid;
        else
            ret = 0;
    }

    return ret;
}

int ftp_control_update_head(int direction, struct ftp_session *session,precord_t * record ){
  int i = 0;
  ya_fvalue_t *value;

  if( session->total_len){
    value = ya_fvalue_new_uinteger(YA_FT_UINT32, session->total_len);
    precord_fvalue_put(record, "Data_totallen", value);
  }
  if( session->real_len){
    value = ya_fvalue_new_uinteger(YA_FT_UINT32, session->real_len);
    precord_fvalue_put(record, "Data_reallen", value);
  }
  if(session->filepath[0]!= '\0'){

  //存盘文件路径
    dpi_fvalue_new_string_put(record, YA_FT_STRING, "localFilename", session->filepath + strlen(g_config.tbl_out_dir) + 1,
        strlen(session->filepath + strlen(g_config.tbl_out_dir) + 1));
    dpi_fvalue_new_string_put(record, YA_FT_STRING, "localFilepath", session->filepath, strlen(session->filepath));
    FILE  *fp = fopen(session->filepath, "r");
    char   con[129] = {0};
    size_t len = fread(con, 128, 1, fp);
    dpi_fvalue_new_string_put(record, YA_FT_STRING, "ftp_file_content", con, strlen(con));
    //存盘文件名
    if (strlen(session->filepath)) {
            int path_len = 0;
            for (path_len = strlen(session->filepath); path_len > 0; path_len--) {
                if (session->filepath[path_len] == '/')
                    break;
            }
            path_len++;
            dpi_fvalue_new_string_put(
                record, YA_FT_STRING, "Data_filepath", session->filepath + path_len, strlen(session->filepath) - path_len);

  }
  }

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"ftp_operations", session->trans_cmd, (strlen(session->trans_cmd)) > 1 ? (strlen(session->trans_cmd)) : 0);
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Control_cmd", session->reqCmd, (strlen(session->reqCmd)) > 1 ? (strlen(session->reqCmd) - 1) : 0);

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Control_args", session->reqArg, (strlen(session->reqArg)) > 1 ? (strlen(session->reqArg) - 1) : 0);

  dpi_fvalue_new_string_put(record,YA_FT_STRING, "Return_code",session->resCode, (strlen(session->resCode)) > 1 ? (strlen(session->resCode) - 1) : 0);
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Return_content", session->resArg, (strlen(session->resArg)) > 1 ? (strlen(session->resArg) - 1) : 0);

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Username", session->username, strlen(session->username));

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Password", session->password, strlen(session->password));

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Filename", session->storpath, strlen(session->storpath));
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Data_filetype", session->filetype, strlen(session->filetype));

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Content_type", session->con_type, strlen(session->con_type));
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"hostName", session->hostName, strlen(session->hostName));
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"software", session->software, strlen(session->software));

  char ip_str[256] = {0};
  if(4 == session->control_ipVer){
  get_ip4string(
      ip_str, sizeof(ip_str), direction ? session->control_conv_tuple.ip_dst.ip4 : session->control_conv_tuple.ip_src.ip4);
  }else if (6 == session->control_ipVer) {
  get_ip6string(
      ip_str, sizeof(ip_str), direction ? session->control_conv_tuple.ip_dst.ip6 : session->control_conv_tuple.ip_src.ip6);
  }
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Server_IP", ip_str, strlen(ip_str));
  if(session->ftp_data_ip.ip4){
    if (4 == session->control_ipVer) {
    get_ip4string(ip_str, sizeof(ip_str), session->ftp_data_ip.ip4);
    } else if (6 == session->control_ipVer) {
    get_ip6string(ip_str, sizeof(ip_str), session->ftp_data_ip.ip6);
    }
    dpi_fvalue_new_string_put(record,YA_FT_STRING,"DataIp", ip_str, strlen(ip_str));
  }

  if(session->dataPort){
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, session->dataPort);
    precord_fvalue_put(record, "DataPort", value);
  }

  if (!memcmp(session->login_status, "YES", 3)) {
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, 1);
  }else {
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, 0);
  }
  precord_fvalue_put(record, "LoginStatus", value);


  value = ya_fvalue_new_uinteger(YA_FT_UINT8,session->mode);
  precord_fvalue_put(record, "Mode", value);

  return 0;
}

static int ftp_control_put_head(struct flow_info *flow, int direction, struct ftp_session *session,precord_t * record ){
  int i = 0;
  ya_fvalue_t * value;

  if( session->total_len){
    value = ya_fvalue_new_uinteger(YA_FT_UINT32, session->total_len);
    precord_fvalue_put(record, "Data_totallen", value);
  }

  value = ya_fvalue_new_uinteger(YA_FT_UINT8,session->mode);
  precord_fvalue_put(record, "Mode", value);

 //存盘文件路径
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"localFilename", session->filepath + strlen(session->filepath), strlen(session->filepath) - strlen(session->filepath));
  dpi_fvalue_new_string_put(record, YA_FT_STRING, "localFilepath", session->filepath, strlen(session->filepath));


 //存盘文件名
  if(strlen(session->filepath)){
    int path_len = 0 ;
    for(path_len = strlen(session->filepath); path_len> 0 ;path_len --)
    {
      if(session->filepath[path_len] == '/')
        break;
    }
    path_len++;
    dpi_fvalue_new_string_put(record,YA_FT_STRING,"Data_filepath", session->filepath+path_len, strlen(session->filepath)-path_len);
  }
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Username", session->username, strlen(session->username));

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Password", session->password, strlen(session->password));

  if(strcmp(session->storpath, "file.path_list") != 0){
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Filename", session->storpath, strlen(session->storpath));
  }
  if (strlen(session->reqCmd) > 1) {
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Control_cmd", session->reqCmd, strlen(session->reqCmd) - 1);
  }
  if (strlen(session->reqArg)  > 1) {
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Control_args", session->reqArg, strlen(session->reqArg) - 1);
  }
  if (strlen(session->resCode) > 1) {
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Return_code", session->resCode, strlen(session->resCode) - 1);
  }
  if (strlen(session->resArg)  > 1) {
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Return_content",session->resArg, (strlen(session->resArg) - 1));
  }

  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Content_type", session->con_type, strlen(session->con_type));
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"hostName", session->hostName, strlen(session->hostName));
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"software", session->software, strlen(session->software));
  char ip_str[256] = {0};
  if(4 == session->control_ipVer){
  get_ip4string(
      ip_str, sizeof(ip_str), direction ? session->control_conv_tuple.ip_dst.ip4 : session->control_conv_tuple.ip_src.ip4);
  }else if (6 == session->control_ipVer) {
  get_ip6string(
      ip_str, sizeof(ip_str), direction ? session->control_conv_tuple.ip_dst.ip6 : session->control_conv_tuple.ip_src.ip6);
  }
  dpi_fvalue_new_string_put(record,YA_FT_STRING,"Server_IP", ip_str, strlen(ip_str));

  if(session->ftp_data_ip.ip4){
    if(4 == session->control_ipVer){
      get_ip4string(ip_str,sizeof(ip_str), session->ftp_data_ip.ip4);
    }else if (6 == session->control_ipVer) {
      get_ip6string(ip_str,sizeof(ip_str), session->ftp_data_ip.ip6);
    }
    dpi_fvalue_new_string_put(record,YA_FT_STRING,"DataIp", ip_str, strlen(ip_str));
  }
  if(session->dataPort){
    value = ya_fvalue_new_uinteger(YA_FT_UINT8,session->dataPort);
    precord_fvalue_put(record, "DataPort", value);
  }


  if (!memcmp(session->login_status, "YES", 3)) {
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, 1);
  }else {
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, 0);
  }



  return 0;
}

void write_ftp_control_conversation_log(const struct conversation_tuple * tuple,void *session) {
  struct tbl_log *log_ptr;
  struct ftp_session *ftp_session = session;
  const char *postfix;
    if(ftp_session->data_conv_tuple.proto){
    memset(&ftp_session->data_conv_tuple,0,sizeof(ftp_session->data_conv_tuple));
    }
    if (ftp_session->control_conv_tuple.proto) {
    struct conversation_value *tmp = find_conversation(&ftp_session->control_conv_tuple, 0);
    if (tmp) {
      tmp->createtime = time(NULL);
      struct ftp_session *s = tmp->conv_session;
      if (s) {
        uint16_t src_port = ntohs(tuple->port_src);
        printf("FTP: Conv timeout processing, looking for filename with port %d\n", src_port);

        struct ftp_port_filename *filename_entry = ftp_filename_list_find_by_port(&s->filename_list, src_port);
        if (filename_entry && filename_entry->dataPort != 0) {
          printf( "FTP: Found filename entry for port %d: '%s'",
                  src_port, filename_entry->storpath);

          if (strlen(filename_entry->storpath)) {
            postfix = strrchr(filename_entry->storpath, '.');
            if (postfix) {
              char filetype[COMMON_SOME_TYPE] = {0};
              printf("FTP: Extracted extension '%s' from '%s'",
                      postfix + 1, filename_entry->storpath);

              if (strcmp(postfix + 1, ftp_session->filetype) != 0) {
                strncpy(ftp_session->filetype, postfix + 1, COMMON_SOME_TYPE);
                printf( "FTP: Updated filetype to '%s'\n", ftp_session->filetype);

                char *postfix_ = strrchr(ftp_session->filepath, '.');
                if (postfix_ != NULL) {
                  char new_storpath[COMMON_FILE_PATH] = {0};
                  strncpy(new_storpath, ftp_session->filepath, postfix_ - ftp_session->filepath);
                  strcat(new_storpath, postfix);
                  rename(ftp_session->filepath, new_storpath);
                  snprintf(ftp_session->filepath, sizeof(ftp_session->filepath), "%s", new_storpath);
                  memset(ftp_session->storpath, 0, sizeof(ftp_session->storpath));
                  strncpy(ftp_session->storpath, filename_entry->storpath, COMMON_SOME_TYPE);
                  printf( "FTP: Renamed file to '%s'\n", new_storpath);
                }
              }
            } else {
              printf( "FTP: No extension found in filename '%s'\n", filename_entry->storpath);
            }
          } else {
            printf( "FTP: Empty storpath for port %d\n", src_port);
          }
        } else {
          printf( "FTP: No filename entry found for port %d\n", src_port);
        }
      }
    }
    }

  if(!ftp_session->record)
    return;
  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    printf( "not enough memory\n");
    return;
  }
  init_log_ptr_data(log_ptr, ftp_session->flow,EM_TBL_LOG_ID_BY_DEFAULT);
  // if (strcmp(ftp_session->filetype,"text")==0) {
  //   if (strlen(ftp_session->storpath) >0 && (postfix = strrchr(ftp_session->storpath, '.'))) {
  //       memset(ftp_session->filetype, 0, sizeof(ftp_session->filetype));
  //       strncpy(ftp_session->filetype, postfix + 1, COMMON_SOME_TYPE);
  //       if(strlen(ftp_session->filepath)>0){
  //             char new_name[512] = {0};
  //             strncpy(new_name, ftp_session->filepath, strlen(ftp_session->filepath) - 4);
  //             strcat(new_name, ftp_session->filetype);
  //             rename(ftp_session->filepath,new_name);
  //             memcpy(ftp_session->filepath,new_name,sizeof(ftp_session->filepath));
  //       }
  //   }


  // }
  ftp_control_update_head(0, ftp_session, ftp_session->record);

  log_ptr->record = ftp_session->record;
  log_ptr->log_type = PROTOCOL_FTP_CONTROL;
  dpi_flow_free(ftp_session->flow, dpi_flow_timeout_free);

  if (write_tbl_log(log_ptr) != 1) {
    if (ftp_session->record) {
        precord_destroy(ftp_session->record);
        ftp_session->record = NULL;
    }
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    return;
  }

  ftp_session->record = NULL;
  return;
}

/*
*ftp的tbl日志函数
*/
static void write_ftp_control_log(struct flow_info *flow, int direction, struct ftp_session *session)
{
    int idx = 0;
    struct tbl_log *log_ptr;
    // char __str[64]={0};
    // struct ftp_session *session = (struct ftp_session *)flow->app_session;
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        printf( "not enough memory\n");
        return ;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ftp");

    //在超时时尝试获取已经写过的文件路径
    if (session->data_conv_tuple.proto) {
      struct conversation_value *tmp = find_conversation(&session->data_conv_tuple, NO_PORT_B);
      if(tmp){
          tmp->createtime = time(NULL);;
          struct ftp_session *s = tmp->conv_session;
          strncpy(session->filetype, s->filetype,sizeof(session->filetype));
          strncpy(session->filepath, s->filepath, sizeof(session->filepath));
          session->total_len = s->total_len;
          session->real_len = s->real_len;
      }
    }
    ftp_control_put_head(flow, direction, session,log_ptr->record);
    log_ptr->log_type = PROTOCOL_FTP_CONTROL;
    if (write_tbl_log(log_ptr) != 1){
        sdt_precord_destroy(log_ptr->record);
        session->record =NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
        return;
    }
    session->record = NULL;
    return;
}

/**
 * @brief 创建session中的record函数
 *        在创建conversion前执行，执行时会将此record拷贝到conversion->conv_session中，所以需要在每次创建conversion时创建新的record
 *        需要在session结束时销毁，才能再下一个新的conversion到来时创建新record
 * @param flow
 * @param session
 * @return int
 */
static int ftp_creat_session_record(struct flow_info *flow, struct ftp_session *session){
  if(session->record == NULL){
    dpi_precord_new_record(session->record, NULL, NULL);
    struct tbl_log log_ptr;//将record保存在临时tbl_log中，用于写公共字段
    int idx = 0;
    log_ptr.record = session->record;
    write_tbl_log_common(flow, 0, &log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(session->record, "ftp");
    session->flow = dpi_flow_clone(flow);
  }
    return 0;
}

static int ftp_update_conversion_session_data(struct ftp_session *dst_sesseion, struct ftp_session *src_session) {
    if (NULL == dst_sesseion) {
    return 0;
    }
    memcpy(dst_sesseion->username, src_session->username, sizeof(dst_sesseion->username));
    memcpy(dst_sesseion->password, src_session->password, sizeof(dst_sesseion->password));
    memcpy(dst_sesseion->hostName, src_session->hostName, sizeof(dst_sesseion->hostName));
    memcpy(dst_sesseion->software, src_session->software, sizeof(dst_sesseion->software));
    if(strlen(dst_sesseion->storpath) == 0 ||strcmp(dst_sesseion->storpath, "file.path_list") == 0){
      memcpy(dst_sesseion->storpath, src_session->storpath, sizeof(dst_sesseion->storpath));
    }
    if(strlen(dst_sesseion->filepath) == 0){
      memcpy(dst_sesseion->filepath, src_session->filepath, sizeof(dst_sesseion->filepath));
    }
    memcpy(dst_sesseion->reqArg, src_session->reqArg, sizeof(dst_sesseion->reqArg));
    memcpy(dst_sesseion->reqCmd, src_session->reqCmd, sizeof(dst_sesseion->reqCmd));
    memcpy(dst_sesseion->resArg, src_session->resArg, sizeof(dst_sesseion->resArg));
    memcpy(dst_sesseion->resCode, src_session->resCode, sizeof(dst_sesseion->resCode));
    memcpy(dst_sesseion->trans_cmd, src_session->trans_cmd, sizeof(dst_sesseion->trans_cmd));
    memcpy(&dst_sesseion->data_conv_tuple ,& src_session->data_conv_tuple,sizeof(src_session->data_conv_tuple));
    memcpy(&dst_sesseion->control_conv_tuple ,& src_session->control_conv_tuple,sizeof(src_session->control_conv_tuple));

    // 复制动态文件名列表
    if (src_session->filename_list.filenames && src_session->filename_list.count > 0) {
        if (dst_sesseion->filename_list.filenames == NULL) {
            ftp_filename_list_init(&dst_sesseion->filename_list);
        }

        // 安全检查：确保源数据有效
        if (src_session->filename_list.filenames == NULL || src_session->filename_list.count == 0) {
            printf("FTP: Source filename list is empty or invalid, skipping copy\n");
            return 1;
        }

        printf("FTP: Copying filename list: src_count=%d, dst_capacity=%d\n",
               src_session->filename_list.count, dst_sesseion->filename_list.capacity);

        // 确保目标有足够容量
        while (dst_sesseion->filename_list.capacity < src_session->filename_list.count) {
            if (ftp_filename_list_expand(&dst_sesseion->filename_list) < 0) {
                printf("FTP: Failed to expand destination filename list\n");
                return -1;
            }
        }

        // 安全的内存复制
        if (dst_sesseion->filename_list.filenames != NULL && src_session->filename_list.filenames != NULL) {
            memcpy(dst_sesseion->filename_list.filenames, src_session->filename_list.filenames,
                   src_session->filename_list.count * sizeof(struct ftp_port_filename));
            dst_sesseion->filename_list.count = src_session->filename_list.count;
            printf("FTP: Successfully copied %d filename entries\n", src_session->filename_list.count);
        } else {
            printf("FTP: Invalid pointers, cannot copy filename list\n");
            return -1;
        }
    }
    return 1;
}

static int ftp_find_conv_update_conversion_session_data(struct flow_info *flow, struct ftp_session *session) {
  if (!session->data_conv_tuple.proto) {
    return 0;
  }
    struct conversation_value *tmp = find_conversation(&session->data_conv_tuple, NO_PORT_B);
    if (tmp) {
    tmp->createtime = time(NULL);
    struct ftp_session *s = tmp->conv_session;
    ftp_update_conversion_session_data(s, session);
    }
    tmp = find_conversation(&session->control_conv_tuple, 0);
    if (tmp) {
    tmp->createtime = time(NULL);
    struct ftp_session *s = tmp->conv_session;
    ftp_update_conversion_session_data(s, session);
    }
    return 1;
}

/**
 * @brief 查找创建ftp_data的conversion，现在创建确定双向端口的conv
 *
 * @param flow
 * @param session
 * @param tuple
 * @return struct conversation_value*
 */
static struct conversation_value *  ftp_find_or_create_conversation(struct flow_info *flow, struct ftp_session *session,struct conversation_tuple *tuple){
    //创建session的record
     struct conversation_value * conv  = NULL;

    // 首先尝试查找确定双向端口的conv
    if((conv = find_conversation(tuple, 0)) != NULL) {
        conv->createtime = g_config.g_now_time;
        return conv;
    }

    // 如果没找到，再查找无端口的conv（兼容旧逻辑）
    if((conv = find_conversation(tuple, NO_PORT_B)) != NULL) {
        conv->createtime = g_config.g_now_time;
        return conv;
    }

    ftp_creat_session_record(flow,session);

    // 创建确定双向端口的conv，而不是无端口的conv
    conv = find_or_create_conversation(tuple, 0, PROTOCOL_FTP_DATA, session);
    //session的record地址会被拷贝进入conv_session中，所以session的record置空
    session->record = NULL;
    return conv;
}

/**
 * @brief 统一接口：查找最新的有端口但无文件名的条目并更新文件名
 *
 * @param list 文件名列表
 * @param storpath 要设置的文件路径
 * @param flow 流信息（用于conv更新，可为NULL）
 * @param session 会话信息
 * @return int 成功返回0，失败返回-1
 */
static int ftp_filename_list_update_latest_empty_storpath(struct ftp_filename_list *list, const char *storpath,
                                                         struct flow_info *flow, struct ftp_session *session) {
    if (!list || !storpath || !session) {
        printf( "FTP: Invalid parameters for update_latest_empty_storpath\n");
        return -1;
    }

    printf("FTP: Searching for empty storpath to update with '%s', list count=%d",
            storpath, list->count);

    // 查找最新的有端口但无文件名的条目（从后往前查找）
    for (int i = list->count - 1; i >= 0; i--) {
        printf("FTP: Checking index %d: port=%d, storpath='%s'",
                i, list->filenames[i].dataPort, list->filenames[i].storpath);

        if (list->filenames[i].dataPort != 0 && list->filenames[i].storpath[0] == '\0') {
            // 更新文件名
            strncpy(list->filenames[i].storpath, storpath,
                    sizeof(list->filenames[i].storpath) - 1);
            list->filenames[i].storpath[sizeof(list->filenames[i].storpath) - 1] = '\0';

            printf( "FTP: Updated empty storpath at index %d (port %d) with '%s'",
                    i, list->filenames[i].dataPort, storpath);

            // 如果提供了flow信息，则更新conv
            if (flow) {
                struct conversation_tuple tuple;
                memcpy(&tuple, &session->data_conv_tuple, sizeof(tuple));
                tuple.port_src = htons(list->filenames[i].dataPort);

                // 查找对应端口的data conv并更新
                struct conversation_value *data_conv = find_conversation(&tuple, 0);
                if (data_conv && data_conv->conv_session) {
                    struct ftp_session *data_session = data_conv->conv_session;
                    // 更新data session的文件名信息
                    strncpy(data_session->storpath, storpath, sizeof(data_session->storpath) - 1);
                    data_session->storpath[sizeof(data_session->storpath) - 1] = '\0';
                    printf("FTP: Updated data conv session storpath for port %d: '%s'",
                            list->filenames[i].dataPort, storpath);
                }

                // 也更新或创建control conv
                struct conversation_value *conv = ftp_find_or_create_conversation(flow, session, &session->data_conv_tuple);
                if (conv) {
                    struct ftp_session *conv_s = conv->conv_session;
                    ftp_update_conversion_session_data(conv_s, session);
                    printf("FTP: Updated control conv session data for port %d\n", list->filenames[i].dataPort);
                }
            }
            return 0;
        }
    }
    printf( "FTP: No empty storpath entry found for update with '%s'\n", storpath);
    return -1; // 没有找到合适的条目
}

/**
 * @brief 强制更新最新的端口条目的文件名（用于重组处理，覆盖单包处理的结果）
 *
 * @param list 文件名列表
 * @param storpath 要设置的文件路径
 * @param flow 流信息（用于conv更新，可为NULL）
 * @param session 会话信息
 * @return int 成功返回0，失败返回-1
 */
static int ftp_filename_list_update_latest_storpath_force(struct ftp_filename_list *list, const char *storpath,
                                                         struct flow_info *flow, struct ftp_session *session) {
    if (!list || !storpath || !session) {
        printf( "FTP: Invalid parameters for update_latest_storpath_force\n");
        return -1;
    }

    printf("FTP: Force updating latest storpath with '%s', list count=%d\n",
            storpath, list->count);

    // 查找最新的端口条目（从后往前查找），无论是否已有文件名都进行覆盖
    for (int i = list->count - 1; i >= 0; i--) {
        printf("FTP: Checking index %d for force update: port=%d, old_storpath='%s'\n",
                i, list->filenames[i].dataPort, list->filenames[i].storpath);

        if (list->filenames[i].dataPort != 0) {
            char old_storpath[COMMON_FILE_PATH];
            strncpy(old_storpath, list->filenames[i].storpath, sizeof(old_storpath) - 1);
            old_storpath[sizeof(old_storpath) - 1] = '\0';

            // 强制更新文件名（覆盖已有的）
            strncpy(list->filenames[i].storpath, storpath,
                    sizeof(list->filenames[i].storpath) - 1);
            list->filenames[i].storpath[sizeof(list->filenames[i].storpath) - 1] = '\0';

            printf("FTP: Force updated storpath at index %d (port %d): '%s' -> '%s'\n",
                    i, list->filenames[i].dataPort, old_storpath, storpath);

            // 如果提供了flow信息，则更新conv
            if (flow) {
                struct conversation_tuple tuple;
                memcpy(&tuple, &session->data_conv_tuple, sizeof(tuple));
                tuple.port_src = htons(list->filenames[i].dataPort);

                // 查找对应端口的data conv并更新
                struct conversation_value *data_conv = find_conversation(&tuple, 0);
                if (data_conv && data_conv->conv_session) {
                    struct ftp_session *data_session = data_conv->conv_session;
                    // 强制更新data session的文件名信息
                    strncpy(data_session->storpath, storpath, sizeof(data_session->storpath) - 1);
                    data_session->storpath[sizeof(data_session->storpath) - 1] = '\0';
                    printf("FTP: Force updated data conv session storpath for port %d: '%s'\n",
                            list->filenames[i].dataPort, storpath);
                }

                // 也更新或创建control conv
                struct conversation_value *conv = ftp_find_or_create_conversation(flow, session, &session->data_conv_tuple);
                if (conv) {
                    struct ftp_session *conv_s = conv->conv_session;
                    ftp_update_conversion_session_data(conv_s, session);
                    printf("FTP: Force updated control conv session data for port %d\n", list->filenames[i].dataPort);
                }
            }
            return 0;
        }
    }
    printf( "FTP: No port entry found for force update with '%s'\n", storpath);
    return -1; // 没有找到合适的条目
}

/**
 * @brief 更新所有关联的data_conv，在control超时时调用
 *
 * @param control_session control流的session
 */
static void ftp_update_all_data_convs(struct ftp_session *control_session) {
    if (!control_session || !control_session->filename_list.filenames) {
        printf( "FTP: Invalid control session or empty filename list for data conv update\n");
        return;
    }

    printf( "FTP: Updating all data convs, filename_list count=%d",
            control_session->filename_list.count);

    // 遍历所有文件端口，查找并更新对应的data_conv
    for (int i = 0; i < control_session->filename_list.count; i++) {
        struct ftp_port_filename *filename_entry = &control_session->filename_list.filenames[i];
        if (filename_entry->dataPort == 0) {
            printf("FTP: Skipping empty port at index %d\n", i);
            continue;
        }

        printf("FTP: Processing data conv for port %d, storpath='%s'",
                filename_entry->dataPort, filename_entry->storpath);

        // 构造data流的tuple来查找对应的conv
        struct conversation_tuple data_tuple;
        memcpy(&data_tuple, &control_session->data_conv_tuple, sizeof(data_tuple));
        data_tuple.port_src = htons(filename_entry->dataPort);

        // 查找确定端口的conv
        struct conversation_value *data_conv = find_conversation(&data_tuple, 0);
        if (data_conv) {
            data_conv->createtime = time(NULL);
            struct ftp_session *data_session = data_conv->conv_session;
            if (data_session) {
                // 更新data session的信息
                ftp_update_conversion_session_data(data_session, control_session);
                printf( "FTP: Updated data conv for port %d\n", filename_entry->dataPort);
            } else {
                printf( "FTP: Data conv found but no session for port %d\n", filename_entry->dataPort);
            }
        } else {
            printf( "FTP: No data conv found for port %d\n", filename_entry->dataPort);
        }
    }
    printf( "FTP: Finished updating all data convs\n");
}

/*
*ftp-control的实时单包解析入口函数
*/
static int dissect_ftp_control(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
    //通过该函数按包到达顺序先行建立ftp-data的conv建立
    uint16_t            ftp_data_port = 0;
    uint32_t            ftp_data_ip = 0;
    uint8_t             is_port_request = 0;
    uint8_t             is_eprt_request = 0;
    uint8_t             is_pasv_response = 0;
    uint8_t             is_epasv_response = 0;
    struct ftp_session *session;  // add by liugh
    const uint8_t      *line;
    char                code_str[4];
    int                 code;
    int                 linelen;
    uint32_t       tokenlen = 0;
    const uint8_t *next_token;
    line = payload;
    char line_[1024] = {0};
    snprintf(line_,payload_len,"%s",payload);
    printf("FTP: dissect_ftp_control [%s] (len=%d)\n" ,line_, payload_len);

    // 额外打印原始数据的十六进制表示，用于调试
    printf("FTP: Raw payload: ");
    for (int i = 0; i < payload_len && i < 50; i++) {
        printf("%02x ", payload[i]);
    }
    printf("\n");

    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct ftp_session));
        if (flow->app_session == NULL) {
            printf( "malloc failed\n");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct ftp_session));
        session = (struct ftp_session *)flow->app_session;
        session->creatime = time(NULL);
        // 初始化动态文件名列表
        if (ftp_filename_list_init(&session->filename_list) < 0) {
            printf( "failed to init filename list\n");
            dpi_free(flow->app_session);
            flow->app_session = NULL;
            return PKT_OK;
        }
    }

    session = (struct ftp_session *)flow->app_session;
    if (session->control_conv_tuple.proto == 0) {
        // 修复端口比较逻辑：正确比较FTP控制端口
        if (ntohs(flow->tuple.inner.port_dst) == g_config.ftp_port) {
            // 客户端到服务器方向：dst是FTP服务器端口(21)
            memcpy(
                session->control_conv_tuple.ip_dst.ip6, flow->tuple.inner.ip_dst, sizeof(session->control_conv_tuple.ip_dst));
            memcpy(
                session->control_conv_tuple.ip_src.ip6, flow->tuple.inner.ip_src, sizeof(session->control_conv_tuple.ip_dst));
            session->control_conv_tuple.port_dst = flow->tuple.inner.port_dst;
            session->control_conv_tuple.port_src = flow->tuple.inner.port_src;
            // 初始化控制端口信息
            session->ftp_control_port_dst = flow->tuple.inner.port_dst;  // 21
            session->ftp_control_port_src = flow->tuple.inner.port_src;  // 客户端端口
            printf("FTP: Initialized as client->server, control_port_dst=%d, control_port_src=%d\n",
                   ntohs(session->ftp_control_port_dst), ntohs(session->ftp_control_port_src));
        } else if (ntohs(flow->tuple.inner.port_src) == g_config.ftp_port) {
            // 服务器到客户端方向：src是FTP服务器端口(21)
            session->control_ipVer = flow->tuple.inner.ip_version;
            memcpy(
                session->control_conv_tuple.ip_src.ip6, flow->tuple.inner.ip_dst, sizeof(session->control_conv_tuple.ip_src));
            memcpy(
                session->control_conv_tuple.ip_dst.ip6, flow->tuple.inner.ip_src, sizeof(session->control_conv_tuple.ip_src));
            session->control_conv_tuple.port_src = flow->tuple.inner.port_dst;
            session->control_conv_tuple.port_dst = flow->tuple.inner.port_src;
            // 初始化控制端口信息
            session->ftp_control_port_dst = flow->tuple.inner.port_src;  // 21
            session->ftp_control_port_src = flow->tuple.inner.port_dst;  // 客户端端口
            printf("FTP: Initialized as server->client, control_port_dst=%d, control_port_src=%d\n",
                   ntohs(session->ftp_control_port_dst), ntohs(session->ftp_control_port_src));
        } else {
            // 都不是FTP控制端口，使用默认逻辑
            printf("FTP: Warning - neither port is FTP control port, using default logic\n");
            session->control_ipVer = flow->tuple.inner.ip_version;
            memcpy(
                session->control_conv_tuple.ip_src.ip6, flow->tuple.inner.ip_dst, sizeof(session->control_conv_tuple.ip_src));
            memcpy(
                session->control_conv_tuple.ip_dst.ip6, flow->tuple.inner.ip_src, sizeof(session->control_conv_tuple.ip_src));
            session->control_conv_tuple.port_src = flow->tuple.inner.port_dst;
            session->control_conv_tuple.port_dst = flow->tuple.inner.port_src;
            // 初始化控制端口信息
            session->ftp_control_port_dst = flow->tuple.inner.port_src;
            session->ftp_control_port_src = flow->tuple.inner.port_dst;
        }
        session->control_conv_tuple.proto = IPPROTO_TCP;
        //只建立conv，不创建record
        struct conversation_value *conv =
            find_or_create_conversation(&session->control_conv_tuple, 0, PROTOCOL_FTP_DATA, session);
        if (conv) {
            struct ftp_session *conv_s = conv->conv_session;
            ftp_update_conversion_session_data(conv_s, session);
        }
    }
    linelen = find_packet_line_end(payload, payload_len);
    // 修复方向判断逻辑：使用FTP控制端口(21)进行判断
    if (ntohs(flow->tuple.inner.port_dst) == g_config.ftp_port) {
      direction = FLOW_DIR_SRC2DST;  // 客户端到服务器
      printf("FTP: Direction SRC2DST (client->server), port_dst=%d, port_src=%d\n",
             ntohs(flow->tuple.inner.port_dst), ntohs(flow->tuple.inner.port_src));
    } else if (ntohs(flow->tuple.inner.port_src) == g_config.ftp_port) {
      direction = FLOW_DIR_DST2SRC;  // 服务器到客户端
      printf("FTP: Direction DST2SRC (server->client), port_dst=%d, port_src=%d\n",
             ntohs(flow->tuple.inner.port_dst), ntohs(flow->tuple.inner.port_src));
    } else {
      // 备用逻辑：使用session中记录的控制端口
      if (flow->tuple.inner.port_dst == session->ftp_control_port_dst) {
        direction = FLOW_DIR_SRC2DST;
        printf("FTP: Direction SRC2DST (fallback), port_dst=%d, control_port_dst=%d\n",
               ntohs(flow->tuple.inner.port_dst), ntohs(session->ftp_control_port_dst));
      } else {
        direction = FLOW_DIR_DST2SRC;
        printf("FTP: Direction DST2SRC (fallback), port_dst=%d, control_port_dst=%d\n",
               ntohs(flow->tuple.inner.port_dst), ntohs(session->ftp_control_port_dst));
      }
    }

    if (direction == FLOW_DIR_SRC2DST) {
        tokenlen = dpi_get_token_len(line, line + linelen, &next_token);
        if (tokenlen != 0) {
            if (strncmp((const char *)line, "PORT", tokenlen) == 0) {
              is_port_request = 1;
              printf("FTP: PORT command detected in SRC2DST direction\n");
            } else if (strncmp((const char *)line, "EPRT", tokenlen) == 0) {
              is_eprt_request = 1;
              printf("FTP: EPRT command detected in SRC2DST direction\n");
            }
        }
        struct ftp_info f_info;
        size_t   copy_len = 0;

        copy_len = tokenlen >= sizeof(f_info.cmd_or_code) ? (sizeof(f_info.cmd_or_code) - 1) : tokenlen;
        strncpy(f_info.cmd_or_code, (const char *)line, copy_len);

        f_info.cmd_or_code[copy_len] = 0;
        //获取请求响应参数
        linelen -= (int)(next_token - line);
        line = next_token;

        if (linelen >= 0) {
          copy_len = (uint32_t)linelen >= sizeof(f_info.args) ? (sizeof(f_info.args) - 1) : (uint32_t)linelen;
          strncpy(f_info.args, (const char *)line, copy_len);
          f_info.args[copy_len] = 0;
          struct conversation_tuple tuple;
          memcpy(&tuple, &session->data_conv_tuple, sizeof(tuple));
          /* retr-download， stou,stor-upload*/
          if ((strncasecmp(f_info.cmd_or_code, "retr", 4) == 0 || strncasecmp(f_info.cmd_or_code, "stor", 4) == 0 ||
               strncasecmp(f_info.cmd_or_code, "stou", 4) == 0)) {
            // 传输端口已经建立确定
            size_t decode_args_len  = sizeof(session->storpath);
            size_t info_args_len    = strlen(f_info.args);
            char   decode_str[1024] = {0};
            if (0 == decode_IOS8859_to_uft8((char *)f_info.args, info_args_len, decode_str, decode_args_len)) {
              strncpy(session->storpath, decode_str, sizeof(session->storpath));
              printf( "FTP: Single packet - decoded filename '%s' (from '%s')",
                      decode_str, f_info.args);
              // 单包处理中不更新文件名，只是为了能够将data流先做关联
              // 文件名更新将在重组处理中进行，以重组顺序为准
              strncpy(f_info.args, session->storpath, sizeof(f_info.args));
            } else {
              strncpy(session->storpath, f_info.args, sizeof(session->storpath));
              printf( "FTP: Single packet - raw filename '%s' (decode failed)",
                      f_info.args);
              // 单包处理中不更新文件名，只是为了能够将data流先做关联
              // 文件名更新将在重组处理中进行，以重组顺序为准
            }
          } else if (strncasecmp(f_info.cmd_or_code, "cwd", 3) == 0 || strncasecmp(f_info.cmd_or_code, "size", 4) == 0) {
            // 切换目录 显示大小 对文件还原不起作用 所以不更新session中的目录
            size_t decode_args_len = sizeof(session->storpath), info_args_len = strlen(f_info.args);
            char   decode_str[1024] = {0};
            if (0 == decode_IOS8859_to_uft8((char *)f_info.args, info_args_len, decode_str, decode_args_len)) {
              strncpy(f_info.args, decode_str, sizeof(session->storpath));
            }
          } else if (strncasecmp(f_info.cmd_or_code, "list", 4) == 0 || strncasecmp(f_info.cmd_or_code, "mlsd", 4) == 0) {
            // 单包处理中不更新文件名，只记录到session中
            // 文件名更新将在重组处理中进行，以重组顺序为准
            strncpy(session->storpath, "file.path_list", sizeof(session->storpath));
          }

          tuple.port_src = htons(ftp_data_port);
        }
    } else {
      if (linelen > 3 && isdigit(line[0]) && isdigit(line[1]) && isdigit(line[2])) {
        memcpy(code_str, line, sizeof(code_str) - 1);
        code_str[sizeof(code_str) - 1] = 0;
        code                           = atoi(code_str);
        if (code == 227) {
          is_pasv_response = 1;
        } else if (code == 229) {
          is_epasv_response = 1;
        } else if (code == 150) {
          // 处理 "150 Opening BINARY mode data connection for filename" 响应
          const char *for_pos = dpi_strnstr_kmp((const char *)line,linelen, " for ");
          if (for_pos) {
            const char *filename_start = for_pos + 5; // 跳过 " for "
            const char *filename_end = dpi_strnstr_kmp(filename_start, linelen - (filename_start - for_pos)," (");
            if (filename_end) {
              int filename_len = filename_end - filename_start;
              if (filename_len > 0 && filename_len < COMMON_FILE_PATH - 1) {
                char filename[COMMON_FILE_PATH];
                strncpy(filename, filename_start, filename_len);
                filename[filename_len] = '\0';

                printf( "FTP: Single packet - extracted filename from 150 response: '%s'\n", filename);

                // 更新session的storpath
                strncpy(session->storpath, filename, sizeof(session->storpath) - 1);
                session->storpath[sizeof(session->storpath) - 1] = '\0';

                // 尝试更新最新的有端口但无文件名的条目
                if (ftp_filename_list_update_latest_empty_storpath(&session->filename_list, filename, flow, session) < 0) {
                  // 如果没有找到空的条目，说明可能还没有PORT命令，先记录文件名到临时存储
                  printf("FTP: No empty port entry found, storing filename '%s' in pending storage\n", filename);
                  strncpy(session->pending_filename, filename, sizeof(session->pending_filename) - 1);
                  session->pending_filename[sizeof(session->pending_filename) - 1] = '\0';
                }
              }
            }
          }
        }
      }
    }

    if (is_port_request || is_pasv_response) {
        parse_port_pasv(line, linelen, &ftp_data_ip, &ftp_data_port);

        memset(&session->data_conv_tuple, 0, sizeof(struct conversation_tuple));
        // 不要重新设置控制端口信息，保持初始化时的正确值
        // session->ftp_control_port_dst = flow->tuple.inner.port_dst;
        // session->ftp_control_port_src = flow->tuple.inner.port_src;

        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;
        tuple.ip_src.ip4 = ftp_data_ip;
        session->ftp_data_ip.ip4 = ftp_data_ip;
        session->ftp_data_ipVer = 4;
        tuple.port_src = htons(ftp_data_port);
        if (direction == FLOW_DIR_SRC2DST)
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        else
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
        //一条ftp控制流中会出现多次port，在此处会更新conv的信息
        memcpy(&session->data_conv_tuple, &tuple, sizeof(tuple));
        struct conversation_value *conv = ftp_find_or_create_conversation(flow, session, &tuple);
        // printf("PASV port = %d\n",ftp_data_port);
        char src_ip[64] = {0};
        char dst_ip[64] = {0};
        get_ip4string(src_ip, 64, tuple.ip_src.ip4);
        get_ip4string(dst_ip, 64, tuple.ip_dst.ip4);
        printf("FTP: Created conversation for PORT/PASV - IP %s:%d -> %s:%d, data_port=%d\n",
               src_ip, ntohs(tuple.port_src), dst_ip, ntohs(tuple.port_dst), ftp_data_port);
        printf("FTP: data_conv_tuple - proto=%d, port_src=%d, port_dst=%d\n",
               session->data_conv_tuple.proto, ntohs(session->data_conv_tuple.port_src), ntohs(session->data_conv_tuple.port_dst));
        if (conv) {
            struct ftp_session *conv_s = conv->conv_session;
            ftp_update_conversion_session_data(conv_s, session);
        }
        session->file_num++;
        session->dataPort = ftp_data_port;
        printf("FTP: Single packet processing - PORT/PASV parsed, file_num=%d, dataPort=%d\n",
                session->file_num, ftp_data_port);

        // 动态添加新的文件端口，如果有等待的文件名则直接关联
        const char *filename_to_add = NULL;
        if (session->pending_filename[0] != '\0') {
            filename_to_add = session->pending_filename;
            printf("FTP: Found pending filename '%s', associating with port %d\n",
                   filename_to_add, ftp_data_port);
            // 清空临时存储
            session->pending_filename[0] = '\0';
        }
        ftp_filename_list_add(&session->filename_list, ftp_data_port, filename_to_add);

        // 添加后立即验证
        printf("FTP: After adding PORT/PASV port %d, filename_list count=%d\n", ftp_data_port, session->filename_list.count);
        for (int i = 0; i < session->filename_list.count; i++) {
            printf("FTP: filename_list[%d]: port=%d, storpath='%s'\n",
                   i, session->filename_list.filenames[i].dataPort, session->filename_list.filenames[i].storpath);
        }
    }

    if (is_eprt_request) {
        uint16_t eprt_ipv6[8];
        uint32_t eprt_af = 0;
        uint32_t eprt_ip;
        parse_eprt_request(line, linelen, &eprt_af, &eprt_ip, eprt_ipv6, &ftp_data_port);

        // 不要重新设置控制端口信息，保持初始化时的正确值
        // session->ftp_control_port_dst = flow->tuple.inner.port_dst;
        // session->ftp_control_port_src = flow->tuple.inner.port_src;

        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;
        if (eprt_af == EPRT_AF_IPv4) {
            tuple.ip_src.ip4 = eprt_ip;
            session->ftp_data_ip.ip4 = ftp_data_ip;
            session->ftp_data_ipVer = 4;

        } else if (eprt_af == EPRT_AF_IPv6) {
            memcpy(tuple.ip_src.ip6, eprt_ipv6, sizeof(tuple.ip_src.ip6));
            memcpy(session->ftp_data_ip.ip6, eprt_ipv6, sizeof(tuple.ip_src.ip6));
            session->ftp_data_ipVer = 6;
        }

        tuple.port_src = htons(ftp_data_port);
        if (direction == FLOW_DIR_SRC2DST)
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        else
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));

        memcpy(&session->data_conv_tuple, &tuple, sizeof(tuple));
        struct conversation_value *conv = ftp_find_or_create_conversation(flow, session, &tuple);
        printf("FTP: Created conversation for EPRT - data_port=%d\n", ftp_data_port);
        printf("FTP: EPRT data_conv_tuple - proto=%d, port_src=%d, port_dst=%d\n",
               session->data_conv_tuple.proto, ntohs(session->data_conv_tuple.port_src), ntohs(session->data_conv_tuple.port_dst));
        session->dataPort = ftp_data_port;
        session->file_num++;

        printf("FTP: Single packet processing - EPRT parsed, file_num=%d, dataPort=%d\n",
                session->file_num, ftp_data_port);

        // 动态添加新的文件端口，如果有等待的文件名则直接关联
        const char *filename_to_add = NULL;
        if (session->pending_filename[0] != '\0') {
            filename_to_add = session->pending_filename;
            printf("FTP: Found pending filename '%s', associating with EPRT port %d\n",
                   filename_to_add, ftp_data_port);
            // 清空临时存储
            session->pending_filename[0] = '\0';
        }
        ftp_filename_list_add(&session->filename_list, ftp_data_port, filename_to_add);

        // 添加后立即验证
        printf("FTP: After adding EPRT port %d, filename_list count=%d\n", ftp_data_port, session->filename_list.count);
        for (int i = 0; i < session->filename_list.count; i++) {
            printf("FTP: filename_list[%d]: port=%d, storpath='%s'\n",
                   i, session->filename_list.filenames[i].dataPort, session->filename_list.filenames[i].storpath);
        }
    }

    if (is_epasv_response) {
        parse_extended_pasv_response(line, linelen, &ftp_data_port);
        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;

        // 不要重新设置控制端口信息，保持初始化时的正确值
        // session->ftp_control_port_dst = flow->tuple.inner.port_dst;
        // session->ftp_control_port_src = flow->tuple.inner.port_src;

        tuple.port_src = htons(ftp_data_port);
        if (direction == FLOW_DIR_SRC2DST) {
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        } else {
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_src));
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
        }

        memcpy(&session->data_conv_tuple, &tuple, sizeof(tuple));
        ftp_find_or_create_conversation(flow, session, &tuple);
        printf("FTP: Created conversation for EPASV - data_port=%d\n", ftp_data_port);
        printf("FTP: EPASV data_conv_tuple - proto=%d, port_src=%d, port_dst=%d\n",
               session->data_conv_tuple.proto, ntohs(session->data_conv_tuple.port_src), ntohs(session->data_conv_tuple.port_dst));
        session->dataPort = ftp_data_port;
        session->file_num++;

        printf("FTP: Single packet processing - EPASV parsed, file_num=%d, dataPort=%d\n",
                session->file_num, ftp_data_port);

        // 动态添加新的文件端口，如果有等待的文件名则直接关联
        const char *filename_to_add = NULL;
        if (session->pending_filename[0] != '\0') {
            filename_to_add = session->pending_filename;
            printf("FTP: Found pending filename '%s', associating with EPASV port %d\n",
                   filename_to_add, ftp_data_port);
            // 清空临时存储
            session->pending_filename[0] = '\0';
        }
        ftp_filename_list_add(&session->filename_list, ftp_data_port, filename_to_add);

        // 添加后立即验证
        printf("FTP: After adding EPASV port %d, filename_list count=%d\n", ftp_data_port, session->filename_list.count);
        for (int i = 0; i < session->filename_list.count; i++) {
            printf("FTP: filename_list[%d]: port=%d, storpath='%s'\n",
                   i, session->filename_list.filenames[i].dataPort, session->filename_list.filenames[i].storpath);
        }
    // printf("ftp_control - data -port = %d\n",ftp_data_port);
    }

    return 0;
}

static int dissect_ftp_server_info(struct ftp_info *info, char *code_str) {
    if (code_str == NULL) {
        return -1;
    }
    char *space_ptr = strchr(code_str, ' ');
    if (space_ptr == NULL) {
        return -1;
    }

    char *server_info_start = space_ptr + 1;
    if (*server_info_start == '(') {
        server_info_start++;
        char *end_ptr = strchr(server_info_start, ')');
        if (end_ptr == NULL) {
            return -1;
        }
        *end_ptr = '\0';
    } else {
        char *end_ptr = strchr(server_info_start, '[');
        if (end_ptr != NULL) {
            *end_ptr = '\0';
        }
    }

    char *token = strtok(server_info_start, " ");
    if (token != NULL) {
        strcpy(info->hostName, token);

        token = strtok(NULL, " ");
        if (token != NULL) {
            strcpy(info->software, token);
        }
    } else {
        return -1;
    }
    return 0;
}

/*
*ftp-control的有序重组解析入口函数
*由于单包处理比重组早到达，移除重复的conv创建逻辑，只保留文件名和命令解析
*/
static int dissect_ftp_control_rsm(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
    uint8_t  is_request;
    size_t   copy_len = 0;
    int      linelen;
    uint32_t tokenlen = 0;
    const uint8_t *next_token;
    const uint8_t *line;
    char code_str[4];
    int code;
    struct ftp_info f_info;
    struct ftp_session *session;  // add by liugh

    memset(&f_info, 0, sizeof(f_info));

    if(direction==FLOW_DIR_SRC2DST)
        is_request = 1;
    else
        is_request = 0;

    f_info.is_request = is_request;
    line = payload;


    char line_[1024] = {0};
    strncpy(line_,payload,payload_len);
    printf("FTP: dissect_ftp_control_rsm %s\n" ,line_);

    if (flow->pkt_first_line.has_search == 0) {
        linelen = find_packet_line_end(payload, payload_len);
        flow->pkt_first_line.has_search = 1;
        flow->pkt_first_line.linelen = linelen;
    } else {
        linelen = payload_len;
    }

    if (linelen <= 0) {
        return PKT_DROP;
    }

    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct ftp_session));
        if (flow->app_session == NULL) {
            printf( "malloc failed\n");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct ftp_session));
        session = (struct ftp_session *)flow->app_session;
        session->creatime = time(NULL);
        // 初始化动态文件名列表
        if (ftp_filename_list_init(&session->filename_list) < 0) {
            printf( "failed to init filename list\n");
            dpi_free(flow->app_session);
            flow->app_session = NULL;
            return PKT_OK;
        }
    }
    session = (struct ftp_session *)flow->app_session;
    #if 0
    if (session->control_conv_tuple.proto == 0) {
        if (ntohs(flow->tuple.inner.port_dst) == htons(g_config.ftp_port)) {
            memcpy(
                session->control_conv_tuple.ip_dst.ip6, flow->tuple.inner.ip_dst, sizeof(session->control_conv_tuple.ip_dst));
            memcpy(
                session->control_conv_tuple.ip_src.ip6, flow->tuple.inner.ip_src, sizeof(session->control_conv_tuple.ip_dst));
            session->control_conv_tuple.port_dst = flow->tuple.inner.port_dst;
            session->control_conv_tuple.port_src = flow->tuple.inner.port_src;
        } else {
            session->control_ipVer = flow->tuple.inner.ip_version;
            memcpy(
                session->control_conv_tuple.ip_src.ip6, flow->tuple.inner.ip_dst, sizeof(session->control_conv_tuple.ip_src));
            memcpy(
                session->control_conv_tuple.ip_dst.ip6, flow->tuple.inner.ip_src, sizeof(session->control_conv_tuple.ip_src));
            session->control_conv_tuple.port_src = flow->tuple.inner.port_dst;
            session->control_conv_tuple.port_dst = flow->tuple.inner.port_src;
        }
        session->control_conv_tuple.proto = IPPROTO_TCP;
        //只建立conv，不创建record
        struct conversation_value *conv =
            find_or_create_conversation(&session->control_conv_tuple, 0, PROTOCOL_FTP_DATA, session);
        if (conv) {
            struct ftp_session *conv_s = conv->conv_session;
            ftp_update_conversion_session_data(conv_s, session);
        }
    }
    #endif
    // printf("%s\n",line);
    //获取请求响应命令
    if (is_request) {
        /*
        * Extract the first token, and, if there is a first
        * token, add it as the request.
        */
        tokenlen = dpi_get_token_len(line, line + linelen, &next_token);
        if (tokenlen != 0) {
            // 移除端口处理逻辑，这些在单包处理中已经处理过了
            // 只保留模式设置用于日志记录
            if (strncmp((const char *)line, "PORT", tokenlen) == 0) {
                session->mode = 0;
            } else if (strncmp((const char *)line, "PASV", tokenlen) == 0 || strncmp((const char *)line, "EPSV", tokenlen) == 0) {
                session->mode = 1;
            } else if (strncmp((const char *)line, "EPRT", tokenlen) == 0) {
                session->mode = 0;
            }

            copy_len = tokenlen >= sizeof(f_info.cmd_or_code) ? (sizeof(f_info.cmd_or_code) - 1) : tokenlen;
            strncpy(f_info.cmd_or_code, (const char *)line, copy_len);

            f_info.cmd_or_code[copy_len] = 0;
        }
    } else {
        /*
        * This is a response; the response code is 3 digits,
        * followed by a space or hyphen, possibly followed by
        * text.
        *
        * If the line doesn't start with 3 digits, it's part of
        * a continuation.
        *
        * XXX - keep track of state in the first pass, and
        * treat non-continuation lines not beginning with digits
        * as errors?
        */
        if (linelen > 3 && isdigit(line[0]) && isdigit(line[1])
                && isdigit(line[2])) {
            /*
            * One-line reply, or first or last line
            * of a multi-line reply.
            */
            memcpy(code_str, line, sizeof(code_str) - 1);
            code_str[sizeof(code_str) - 1] = 0;

            code = atoi(code_str);
            /*
            * See if it's a passive-mode response.
            *
            * XXX - does anybody do FOOBAR, as per RFC
            * 1639, or has that been supplanted by RFC 2428?
            */
            // 移除端口响应处理逻辑，这些在单包处理中已经处理过了
            if (code == 227) {
                // PASV response - 已在单包处理中处理
            } else if (code == 229) {
                // EPSV response - 已在单包处理中处理
            } else if (code == 150) {
                // 处理 "150 Opening BINARY mode data connection for filename" 响应
                const char *for_pos = dpi_strnstr_kmp((const char *)line,linelen, " for ");
                if (for_pos) {
                  const char *filename_start = for_pos + 5; // 跳过 " for "
                  const char *filename_end = dpi_strnstr_kmp(filename_start, linelen - (filename_start - for_pos)," (");
                  if (filename_end) {
                    int filename_len = filename_end - filename_start;
                    if (filename_len > 0 && filename_len < COMMON_FILE_PATH - 1) {
                      char filename[COMMON_FILE_PATH];
                      strncpy(filename, filename_start, filename_len);
                      filename[filename_len] = '\0';

                      printf( "FTP: Reassemble - extracted filename from 150 response: '%s'\n", filename);

                      // 更新session的storpath
                      strncpy(session->storpath, filename, sizeof(session->storpath) - 1);
                      session->storpath[sizeof(session->storpath) - 1] = '\0';

                      // 重组处理中强制更新文件名，覆盖单包处理的结果，以重组顺序为准
                      ftp_filename_list_update_latest_storpath_force(&session->filename_list, filename, flow, session);
                    }
                  }
                }
            }
            else if (code == 230){
                strncpy(session->login_status,"YES",COMMON_STATUS_LEN);
            }
            else if (code == 530){
                strncpy(session->login_status,"NO",COMMON_STATUS_LEN);
                //登入失败
            } else if (code == 257){
                // PWD失败
                // do nothing
            }else if (code == 226) {
              //当收到传输结束的命令时 将控制信令的data端口置空 可以继续下一次建立conv
              memset(&session->data_conv_tuple,0,sizeof(session->data_conv_tuple));
            }else if (code == 220) {
                dissect_ftp_server_info(&f_info,(char*)line);
                strncpy(session->hostName, f_info.hostName, strlen(f_info.hostName));
                strncpy(session->software, f_info.software, strlen(f_info.software));

            }
            /*
            * Skip the 3 digits and, if present, the
            * space or hyphen.
            */
            if (linelen >= 4)
                next_token = line + 4;
            else
                next_token = line + linelen;

            strncpy(f_info.cmd_or_code, (const char *)line, 3);
            f_info.cmd_or_code[3] = 0;
        } else {
            /*
            * Line doesn't start with 3 digits; assume it's
            * a line in the middle of a multi-line reply.
            */
            next_token = line;
        }
    }

    //获取请求响应参数
    linelen -= (int)(next_token - line);
    line = next_token;

    if (linelen >= 0) {
        copy_len = (uint32_t)linelen >= sizeof(f_info.args) ? (sizeof(f_info.args) - 1) : (uint32_t)linelen;
        strncpy(f_info.args, (const char *)line, copy_len);
        f_info.args[copy_len] = 0;
        if(0 != session->data_conv_tuple.proto && is_request){
          //当传输通道已经建立 如果是顺序 更新每次请求的cmd
          memset(&session->trans_cmd,0,sizeof(session->trans_cmd));
          strcpy(session->trans_cmd, f_info.cmd_or_code);
        }
        /* retr-download， stou,stor-upload*/
        if ((   strncasecmp(f_info.cmd_or_code,"retr",4)==0
            || strncasecmp(f_info.cmd_or_code,"stor",4)==0
            || strncasecmp(f_info.cmd_or_code,"stou",4)==0) )
        {
            // 传输端口已经建立确定
            size_t decode_args_len = sizeof(session->storpath);
            size_t info_args_len = strlen(f_info.args);
            char   decode_str[1024] = {0};
            if (0 == decode_IOS8859_to_uft8((char *)f_info.args, info_args_len, decode_str, decode_args_len)) {
              strncpy(session->storpath, decode_str, sizeof(session->storpath));
              printf( "FTP: Reassemble - decoded filename '%s' (from '%s')",
                      decode_str, f_info.args);
              // 重组处理中强制更新文件名，覆盖单包处理的结果，以重组顺序为准
              ftp_filename_list_update_latest_storpath_force(&session->filename_list, decode_str, flow, session);
              strncpy(f_info.args, session->storpath, sizeof(f_info.args));
            } else {
              strncpy(session->storpath, f_info.args, sizeof(session->storpath));
              printf( "FTP: Reassemble - raw filename '%s' (decode failed)",
                      f_info.args);
              // 重组处理中强制更新文件名，覆盖单包处理的结果，以重组顺序为准
              ftp_filename_list_update_latest_storpath_force(&session->filename_list, f_info.args, flow, session);
            }
        } else if(strncasecmp(f_info.cmd_or_code,"cwd",3)  == 0 ||  strncasecmp(f_info.cmd_or_code,"size",4) == 0){
        // 切换目录 显示大小 对文件还原不起作用 所以不更新session中的目录
          size_t  decode_args_len = sizeof(session->storpath),info_args_len = strlen(f_info.args);
          char decode_str[1024] = {0};
          if(0==decode_IOS8859_to_uft8((char *) f_info.args,info_args_len , decode_str, decode_args_len)){
              strncpy(f_info.args, decode_str,sizeof(session->storpath));
          }
        } else if (strncasecmp(f_info.cmd_or_code,"list" ,4)==0 || strncasecmp(f_info.cmd_or_code,"mlsd" ,4)==0)
        {
          // 重组处理中强制更新文件名为目录列表，覆盖单包处理的结果，以重组顺序为准
          ftp_filename_list_update_latest_storpath_force(&session->filename_list, "file.path_list", flow, session);
          strncpy(session->storpath, "file.path_list", sizeof(session->storpath));
        }
    }

    if (is_request) {
        strncpy(f_info.met, f_info.cmd_or_code, sizeof(f_info.cmd_or_code) - 1);
        strncpy(f_info.met_contend, f_info.args, sizeof(f_info.args) - 1);
        if (NULL == dpi_strnstr_kmp(session->reqCmd, sizeof(session->reqCmd), f_info.met)) {
          strcat(session->reqCmd, f_info.met);
          strcat(session->reqCmd, ",");
        }
        if (NULL == dpi_strnstr_kmp(session->reqArg, sizeof(session->reqArg), f_info.met_contend)) {
          strcat(session->reqArg, f_info.met_contend);
          strcat(session->reqArg, ",");
        }
    } else {
        strncpy(f_info.ret_code, f_info.cmd_or_code, sizeof(f_info.ret_code) - 1);
        strncpy(f_info.ret_con, f_info.args, sizeof(f_info.args) - 1);
        if(sizeof(session->resCode) > (strlen(session->resCode)+strlen(f_info.ret_code))+2){
          strncpy(session->resCode+strlen(session->resCode),(char*)f_info.ret_code,sizeof(session->resCode) - strlen(session->resCode));
          strcat(session->resCode, ",");
        }
        if(sizeof(session->resArg) > (strlen(session->resArg)+strlen(f_info.ret_con))+2){
          strncpy(session->resArg+strlen(session->resArg),(char*)f_info.ret_con,sizeof(session->resArg) - strlen(session->resArg));
          strcat(session->resArg, ",");
        }
    }

    if (strcasecmp(f_info.cmd_or_code, "USER") == 0){
        memcpy(session->username, f_info.args, copy_len>sizeof(f_info.username)?(sizeof(f_info.username)-1):copy_len);
    }
    else if (strcasecmp(f_info.cmd_or_code, "PASS") == 0){
        memcpy(session->password, f_info.args, copy_len>sizeof(f_info.password)?(sizeof(f_info.password)-1):copy_len);
    }
    else if (strcasecmp(f_info.cmd_or_code, "REST") == 0)
    {
        session->offset = atoi(f_info.args);
    }
    else if (strcasecmp(f_info.cmd_or_code, "TYPE") == 0)
    {
        if (strcasecmp(f_info.args, "A") == 0 || strcasecmp(f_info.args, "ASCII") == 0)
        {
            strncpy(session->con_type, "ASCII", strlen("ASCII"));
        }
        else if (strcasecmp(f_info.args, "E") == 0 || strcasecmp(f_info.args, "EBCDIC") == 0)
        {
            strncpy(session->con_type, "EBCDIC", strlen("EBCDIC"));
        }
        else if (strcasecmp(f_info.args, "i") == 0 || strcasecmp(f_info.args, "BINARY") == 0)
        {
            strncpy(session->con_type, "BINARY", strlen("BINARY"));
        }
    }


    // 移除所有端口处理逻辑，这些在单包处理中已经处理过了
    // 由于单包处理比重组处理早到达，conv创建和端口解析已经完成

    /*ftp_control流会将信息全部更新到conv的conv_session中，以供data获取*/
    // for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
    //     //更新传输文件端口
    //     printf("[%d] %d , : %s \n", i, session->proto_filename[i].dataPort, session->proto_filename[i].storpath);
    // }
    // printf("ftp_find_conv_update_conversion_session_data \n======================\n");
    ftp_find_conv_update_conversion_session_data(flow,session);

    if(g_config.ftp_packet_mode){
        write_ftp_control_log(flow, direction, session);
    }

    return 0;
}

static void flow_ftp_control_finish(struct flow_info *flow)
{
    if(!flow->app_session)
    {
      return ;
    }
    struct ftp_session        *session = (struct ftp_session *)flow->app_session;

    printf( "FTP: Control flow finishing, file_num=%d, filename_list count=%d",
            session->file_num, session->filename_list.count);

    // 打印文件名列表详情
    for (int i = 0; i < session->filename_list.count; i++) {
        printf("FTP: Filename list[%d]: port=%d, storpath='%s'",
                i, session->filename_list.filenames[i].dataPort,
                session->filename_list.filenames[i].storpath);
    }

    struct conversation_value *tmp = find_conversation(&session->control_conv_tuple, 0);
    if (tmp) {
      tmp->createtime = time(NULL);
      struct ftp_session *s = tmp->conv_session;
      ftp_update_conversion_session_data(s, session);
    }

    // 更新所有关联的data_conv
    ftp_update_all_data_convs(session);

    if (0==session->file_num) {
      //登入无文件传输,在此处写log，有文件动作，在conv超时时写文件（write_ftp_control_conversation_log）
      printf( "FTP: No file transfer, writing control log directly\n");
      write_ftp_control_log(flow, 0, session);
    }

    // 释放动态文件名列表
    ftp_filename_list_free(&session->filename_list);

    free(flow->app_session);
    flow->app_session = NULL;

    return ;
}

/*
*ftp-data的识别函数，不全，没有pasv等的识别，暂时没有使用
*/
static int identify_ftp_data(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    UNUSED(payload);
    UNUSED(payload_len);
    uint16_t s_port = 0, d_port = 0;
    s_port = ntohs(flow->tuple.inner.port_src);
    d_port = ntohs(flow->tuple.inner.port_dst);

    if (s_port == FTP_DATA_PORT || d_port == FTP_DATA_PORT)
        flow->real_protocol_id = PROTOCOL_FTP_DATA;

    return PROTOCOL_FTP_DATA;
}


static void flow_ftp_data_finish(struct flow_info *flow) {
    struct ftp_session *session = (struct ftp_session *)flow->app_session;
    if (!session)
        return ;

    if(session->data_fp){
      fclose(session->data_fp);
      session->data_fp = NULL;
    }
    struct conversation_value *data_conv = NULL;
    // 如果是由ftp_c创建的的流
    if (session->data_conv_tuple.proto) {
      data_conv = find_conversation(&session->data_conv_tuple, NO_PORT_B);
      if (data_conv) {
            struct ftp_session *s = data_conv->conv_session;
            if (s) {
              strncpy(s->filetype, session->filetype, sizeof(s->filetype));
              strncpy(s->filepath, session->filepath, sizeof(s->filepath));
              s->real_len = session->real_len;
              ftp_control_update_head(0, s, s->record);
            }
      }
    } else {
      //通过识别函数创建的流
        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;
        tuple.port_src = flow->tuple.inner.port_src;
        uint8_t direction =
            ntohs(flow->tuple.inner.port_src) < ntohs(flow->tuple.inner.port_dst) ? FLOW_DIR_DST2SRC : FLOW_DIR_SRC2DST;
        if (direction == FLOW_DIR_SRC2DST) {
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        } else {
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
        }
        data_conv = find_conversation(&tuple, NO_PORT_B);
    }
    if (!data_conv) {
        write_ftp_control_log(flow,FLOW_DIR_SRC2DST,session);
    }

    return ;
}
int dissect_ftp_data_miss(struct flow_info *flow, uint8_t direction, uint32_t miss_len) {
    struct ftp_session *session = flow->app_session;
    if (!session) {
      return 0;
    }

    session->total_len += miss_len;
    return 0;
}

static int dissect_ftp_data_rsm(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
    struct ftp_session *session;
    /*一般情况下，此处app_session为ftp_control拷贝进入的session*/
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct ftp_session));
        if (flow->app_session == NULL) {
            printf( "malloc failed\n");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct ftp_session));
        session = (struct ftp_session *)flow->app_session;
        // 初始化动态文件名列表
        if (ftp_filename_list_init(&session->filename_list) < 0) {
            printf( "failed to init filename list\n");
            dpi_free(flow->app_session);
            flow->app_session = NULL;
            return PKT_OK;
        }
    }
    session = (struct ftp_session *)flow->app_session;

    printf("FTP: Data flow processing - port_src=%d, port_dst=%d, direction=%d\n",
           ntohs(flow->tuple.inner.port_src), ntohs(flow->tuple.inner.port_dst), direction);

    session->creatime = time(NULL);
    struct ftp_session        *s = NULL;
    struct conversation_value *control_conv = NULL;
    /*从conv_session中获取ftp_control的信息更新到ftp_data的session中*/
    /*这一步是为了拿到信息处理文件名*/
    if (session->data_conv_tuple.proto) {
        printf("FTP: Using existing data_conv_tuple - proto=%d, port_src=%d, port_dst=%d\n",
               session->data_conv_tuple.proto, ntohs(session->data_conv_tuple.port_src), ntohs(session->data_conv_tuple.port_dst));
        control_conv = find_conversation(&session->data_conv_tuple, NO_PORT_B);
    } else {
        printf("FTP: Constructing tuple for control conv lookup\n");
        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;
        if (direction == FLOW_DIR_DST2SRC) {
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
            tuple.port_src = flow->tuple.inner.port_dst;
            printf("FTP: DST2SRC - constructed tuple port_src=%d\n", ntohs(tuple.port_src));
        } else {
            tuple.port_src = flow->tuple.inner.port_src;
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
            printf("FTP: SRC2DST - constructed tuple port_src=%d\n", ntohs(tuple.port_src));
        }
        printf("FTP: Looking for control conv with tuple port_src=%d\n", ntohs(tuple.port_src));
        control_conv = find_conversation(&tuple, NO_PORT_B);
    }
    if (control_conv) {
        printf("FTP: Found control conv, updating data session\n");
        control_conv->createtime = time(NULL);
        s = control_conv->conv_session;
        if (s->record == NULL) {
            ftp_creat_session_record(flow, session);
        }
        // 先更新session信息，确保我们有最新的文件名列表
        ftp_update_conversion_session_data(session, s);

        printf("FTP: Data flow - updated session from control, filename_list count=%d\n",
                session->filename_list.count);

        // 打印control session的文件名列表
        if (s && s->filename_list.filenames) {
            printf("FTP: Control session filename_list count=%d\n", s->filename_list.count);
            for (int i = 0; i < s->filename_list.count; i++) {
                printf("FTP: Control filename_list[%d]: port=%d, storpath='%s'\n",
                       i, s->filename_list.filenames[i].dataPort, s->filename_list.filenames[i].storpath);
            }
        }
    } else {
        printf("FTP: No control conv found for data flow\n");
    }
    FILE *fp = NULL;
    if (!session->data_fp && g_config.ftp_content) {
        const char *postfix;
        uint16_t src_port = ntohs(flow->tuple.inner.port_src);
        printf("FTP: Data flow processing, looking for filename with port %d\n", src_port);

        // 打印当前session的文件名列表
        printf("FTP: Current session filename_list count=%d\n", session->filename_list.count);
        for (int i = 0; i < session->filename_list.count; i++) {
            printf("FTP: Current filename_list[%d]: port=%d, storpath='%s'\n",
                   i, session->filename_list.filenames[i].dataPort, session->filename_list.filenames[i].storpath);
        }

        // 首先尝试从当前session的文件名列表中查找
        struct ftp_port_filename *filename_entry = ftp_filename_list_find_by_port(&session->filename_list, src_port);
        if (filename_entry) {
            printf("FTP: Found entry in current session for port %d: storpath='%s'\n", src_port, filename_entry->storpath);
        } else {
            printf("FTP: No entry found in current session for port %d\n", src_port);
        }

        // 如果当前session中没有找到，尝试从control session中查找
        if ((!filename_entry || !strlen(filename_entry->storpath)) && s && s->filename_list.filenames) {
            printf("FTP: Searching in control session filename list, count=%d\n", s->filename_list.count);
            filename_entry = ftp_filename_list_find_by_port(&s->filename_list, src_port);
            if (filename_entry) {
                printf("FTP: Found entry in control session for port %d: storpath='%s'\n", src_port, filename_entry->storpath);
            } else {
                printf("FTP: No entry found in control session for port %d\n", src_port);
            }
        }

        if (filename_entry && strlen(filename_entry->storpath)) {
            printf( "FTP: Found filename '%s' for port %d\n", filename_entry->storpath, src_port);

            // 更新session的storpath
            strncpy(session->storpath, filename_entry->storpath, sizeof(session->storpath) - 1);
            session->storpath[sizeof(session->storpath) - 1] = '\0';

            if ((postfix = strrchr(filename_entry->storpath, '.'))){
                strncpy(session->filetype, postfix + 1, COMMON_SOME_TYPE - 1);
                session->filetype[COMMON_SOME_TYPE - 1] = '\0';
                printf( "FTP: Extracted file extension '%s' from '%s'",
                        session->filetype, filename_entry->storpath);
            } else {
                printf( "FTP: No file extension found in '%s'\n", filename_entry->storpath);
                // 如果没有扩展名，设置默认类型
                strncpy(session->filetype, "unknown", COMMON_SOME_TYPE - 1);
                session->filetype[COMMON_SOME_TYPE - 1] = '\0';
            }
        } else {
            printf( "FTP: No filename found for port %d in any session\n", src_port);
            // 设置默认值
            strncpy(session->storpath, "unknown_file", sizeof(session->storpath) - 1);
            session->storpath[sizeof(session->storpath) - 1] = '\0';
            strncpy(session->filetype, "unknown", COMMON_SOME_TYPE - 1);
            session->filetype[COMMON_SOME_TYPE - 1] = '\0';
        }
        if(session->filetype[0] == '\0'){

        /*从session中获得真实文件名，从而获取后缀*/
        // if (0 == strcmp(session->trans_cmd, "LIST") || 0 == strcmp(session->storpath, "file.path_list")) {
        //     if (!g_config.ftp_packet_mode) {
        //       return 0;
        //     }
        // }
        // if (strlen(session->storpath)) {
        //     if ((postfix = strrchr(session->storpath, '.')))
        //       strncpy(session->filetype, postfix + 1, COMMON_SOME_TYPE);
        // }
        /*没在conv中获得到真实文件名，尝试从前4字节获取文件名*/
            if (!detect_file_type((char *)payload, payload_len, session->filetype,sizeof(session->filetype))) {
              if (dpi_is_utf8((char *)payload, payload_len) > 0) {
                    strncpy(session->filetype, "text", COMMON_SOME_TYPE);
              } else {
                    return 0;
              }
            }
        }


        get_special_filename(NULL, "ftp", session->filetype, session->filepath, sizeof(session->filepath), 1);
        session->data_fp = fopen(session->filepath, "w");
    }
    fp = session->data_fp;
    if (fp) {
        fwrite(payload, payload_len, 1, fp);
        fp = NULL;
        if (control_conv) {
            struct ftp_session *s = control_conv->conv_session;
            if (s) {
              // 更新control session的信息
              strncpy(s->filetype, session->filetype, sizeof(s->filetype));
              strncpy(s->filepath, session->filepath, sizeof(s->filepath));
              strncpy(s->storpath, session->storpath, sizeof(s->storpath));
              s->total_len += payload_len;
              session->real_len += payload_len;
              s->real_len = session->real_len;

              printf("FTP: Updated control session - filetype='%s', filepath='%s', storpath='%s'",
                      s->filetype, s->filepath, s->storpath);

              // 更新record
              ftp_control_update_head(0, s, s->record);

              printf( "FTP: Data processing complete - total_len=%d, real_len=%d",
                      s->total_len, s->real_len);
            }
        }
    }
    return 0;
}

extern struct decode_t decode_ftp_c;
extern struct decode_t decode_ftp_u;

static int init_ftp_dissector(struct decode_t *decode) {
    dpi_register_proto_schema(ftp_field_array, EM_FTP_MAX, "ftp");
    session_protocol_st_size[PROTOCOL_FTP_DATA] = sizeof(struct ftp_session);
    register_tbl_array(TBL_LOG_FTP_CONTROL, 1, "ftp", NULL);

    decode_on_port_tcp(g_config.ftp_port, &decode_ftp_c);
    decode_on_port_tcp(FTP_CONTROL_PORT, &decode_ftp_c);
    decode_on_port_tcp(2121, &decode_ftp_c);

    map_fields_info_register(ftp_field_array, PROTOCOL_FTP_CONTROL, EM_FTP_MAX, "ftp");

    // pschema_t * schema = dpi_pschema_get_proto("ftp");
    // pschema_register_field(schema, "localFilename",YA_FT_STRING,"desc");
    // pschema_register_field(schema, "ftp_file_content",YA_FT_STRING,"desc");
    // pschema_register_field(schema, "ftp_operations",YA_FT_STRING,"desc");
    return 0;
}

static int init_ftp_dissector_u(struct decode_t *decode) {
    decode_on_port_tcp(FTP_DATA_PORT, &decode_ftp_u);
    return 0;
}

static int control_destroy(struct decode_t *decode) { return 0; }

struct decode_t decode_ftp_c = {
    .name           =   "ftp_control",
#ifdef DPI_SDT_ZDY
    .identify_type  =   DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial =   init_ftp_dissector,
    .pkt_identify   =   identify_ftp_control,
    .pkt_arrive     =   dissect_ftp_control,
    .pkt_dissect    =   dissect_ftp_control_rsm,
    .pkt_miss       =   dissect_ftp_data_miss,
    .flow_finish    =   flow_ftp_control_finish,
    .decode_destroy =   control_destroy,
};

struct decode_t decode_ftp_u = {
    .name           =   "ftp_data",
#ifdef DPI_SDT_ZDY
    .identify_type  =   DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial =   init_ftp_dissector_u,
    .pkt_identify   =   identify_ftp_data,
    .pkt_dissect    =   dissect_ftp_data_rsm,
    .pkt_miss       =   dissect_ftp_data_miss,
    .flow_finish    =   flow_ftp_data_finish,
    .decode_destroy =   control_destroy,
};
