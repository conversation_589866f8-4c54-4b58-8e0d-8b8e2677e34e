# FTP调试重点关注点

## 问题分析

根据之前的日志，问题的关键在于：
1. 数据流正确识别了端口4170
2. 但在文件名列表中没有找到端口4170对应的文件名
3. 这说明PORT命令处理时，端口4170没有被正确添加到文件名列表中

## 需要重点关注的调试信息

### 1. PORT命令处理流程
查找以下日志序列：
```
FTP: Direction SRC2DST (client->server), port_dst=21, port_src=xxxx
FTP: Created conversation for PORT/PASV - IP x.x.x.x:xxxx -> x.x.x.x:xxxx, data_port=4170
FTP: Single packet processing - PORT/PASV parsed, file_num=1, dataPort=4170
FTP: After adding PORT/PASV port 4170, filename_list count=1
FTP: filename_list[0]: port=4170, storpath=''
```

### 2. 150响应处理流程
查找以下日志序列：
```
FTP: Single packet - extracted filename from 150 response: '100-gdb-tips.pdf'
FTP: Searching for empty storpath to update with '100-gdb-tips.pdf', list count=1
FTP: Checking index 0: port=4170, storpath=''
FTP: Updated empty storpath at index 0 (port 4170) with '100-gdb-tips.pdf'
```

### 3. 数据流处理流程
查找以下日志序列：
```
FTP: Data flow processing - port_src=4170, port_dst=20, direction=x
FTP: Found control conv, updating data session
FTP: Control session filename_list count=1
FTP: Control filename_list[0]: port=4170, storpath='100-gdb-tips.pdf'
FTP: Searching for port 4170 in filename list with 1 entries
FTP: Checking entry 0: port=4170, storpath='100-gdb-tips.pdf'
FTP: Found matching port 4170 at index 0
FTP: Found filename '100-gdb-tips.pdf' for port 4170
```

## 可能的问题点

### 1. PORT命令没有被正确识别
如果看不到"Created conversation for PORT/PASV"日志，说明：
- PORT命令方向判断错误
- PORT命令解析失败
- 端口比较逻辑仍有问题

### 2. 端口没有被添加到文件名列表
如果看不到"After adding PORT/PASV port xxxx"日志，说明：
- ftp_filename_list_add函数没有被调用
- 函数调用失败

### 3. 文件名没有正确关联
如果看到端口被添加但文件名没有关联，说明：
- 150响应处理有问题
- 文件名提取失败
- 更新函数失败

### 4. 数据流查找失败
如果数据流处理时找不到文件名，可能是：
- control conversation查找失败
- session数据没有正确传递
- 文件名列表没有正确复制

## 调试步骤

1. **运行程序并查看日志**
2. **按照上述流程检查每个步骤是否正常**
3. **找到第一个失败的步骤**
4. **针对性地修复该步骤的问题**

## 预期的完整正常流程

```
FTP: Initialized as client->server, control_port_dst=21, control_port_src=xxxx
FTP: Direction SRC2DST (client->server), port_dst=21, port_src=xxxx
FTP: Created conversation for PORT/PASV - IP x.x.x.x:4170 -> x.x.x.x:xxxx, data_port=4170
FTP: Single packet processing - PORT/PASV parsed, file_num=1, dataPort=4170
FTP: After adding PORT/PASV port 4170, filename_list count=1
FTP: filename_list[0]: port=4170, storpath=''
FTP: Single packet - extracted filename from 150 response: '100-gdb-tips.pdf'
FTP: Updated empty storpath at index 0 (port 4170) with '100-gdb-tips.pdf'
FTP: Data flow processing - port_src=4170, port_dst=20, direction=x
FTP: Found control conv, updating data session
FTP: Found filename '100-gdb-tips.pdf' for port 4170
```

如果任何一步缺失或错误，我们就能定位到具体的问题所在。
